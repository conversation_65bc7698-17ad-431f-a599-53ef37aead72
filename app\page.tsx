"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Star, Clock, Shield, TrendingUp, Users, CheckCircle, Zap, Sparkles } from "lucide-react"
import Link from "next/link"
import { motion, useAnimation, useInView } from "framer-motion"
import { useRef } from "react"
import { useAuth } from "@/lib/auth"
import SocialLinks from "@/components/social-links"

// Animated Background Component
const AnimatedBackground = () => {
  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none">
      {/* Animated gradient orbs */}
      <motion.div
        className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-cyan-400/20 to-blue-600/20 rounded-full blur-3xl"
        animate={{
          x: [0, 100, 0],
          y: [0, -100, 0],
          scale: [1, 1.2, 1],
        }}
        transition={{
          duration: 20,
          repeat: Number.POSITIVE_INFINITY,
          ease: "easeInOut",
        }}
      />
      <motion.div
        className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-purple-400/20 to-pink-600/20 rounded-full blur-3xl"
        animate={{
          x: [0, -100, 0],
          y: [0, 100, 0],
          scale: [1, 1.3, 1],
        }}
        transition={{
          duration: 25,
          repeat: Number.POSITIVE_INFINITY,
          ease: "easeInOut",
        }}
      />
      <motion.div
        className="absolute top-1/2 left-1/2 w-60 h-60 bg-gradient-to-r from-green-400/10 to-cyan-600/10 rounded-full blur-3xl"
        animate={{
          x: [0, 150, -150, 0],
          y: [0, -150, 150, 0],
          scale: [1, 0.8, 1.1, 1],
        }}
        transition={{
          duration: 30,
          repeat: Number.POSITIVE_INFINITY,
          ease: "easeInOut",
        }}
      />
    </div>
  )
}

// Floating Particles Component
const FloatingParticles = () => {
  const particles = Array.from({ length: 50 }, (_, i) => i)

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none">
      {particles.map((particle) => (
        <motion.div
          key={particle}
          className="absolute w-1 h-1 bg-cyan-400/30 rounded-full"
          initial={{
            x: Math.random() * (typeof window !== "undefined" ? window.innerWidth : 1000),
            y: Math.random() * (typeof window !== "undefined" ? window.innerHeight : 1000),
          }}
          animate={{
            y: [null, -100],
            opacity: [0, 1, 0],
          }}
          transition={{
            duration: Math.random() * 10 + 10,
            repeat: Number.POSITIVE_INFINITY,
            delay: Math.random() * 10,
          }}
        />
      ))}
    </div>
  )
}

// Neon Text Component
const NeonText = ({ children, className = "" }: { children: React.ReactNode; className?: string }) => {
  return (
    <motion.div
      className={`relative ${className}`}
      whileHover={{ scale: 1.05 }}
      transition={{ type: "spring", stiffness: 300 }}
    >
      <div className="absolute inset-0 text-cyan-400 blur-sm opacity-75">{children}</div>
      <div className="relative text-cyan-400 drop-shadow-[0_0_10px_rgba(6,182,212,0.5)]">{children}</div>
    </motion.div>
  )
}

// Animated Counter Component
const AnimatedCounter = ({ end, duration = 2 }: { end: number; duration?: number }) => {
  const [count, setCount] = useState(0)
  const controls = useAnimation()
  const ref = useRef(null)
  const inView = useInView(ref)

  useEffect(() => {
    if (inView) {
      const timer = setInterval(() => {
        setCount((prev) => {
          if (prev < end) {
            return Math.min(prev + Math.ceil(end / (duration * 60)), end)
          }
          clearInterval(timer)
          return end
        })
      }, 1000 / 60)

      return () => clearInterval(timer)
    }
  }, [inView, end, duration])

  return <span ref={ref}>{count.toLocaleString()}</span>
}

export default function HomePage() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const { user } = useAuth()

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }

    window.addEventListener("mousemove", handleMouseMove)
    return () => window.removeEventListener("mousemove", handleMouseMove)
  }, [])

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
      },
    },
  }

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden"
      dir="rtl"
    >
      <AnimatedBackground />
      <FloatingParticles />

      {/* Cursor follower */}
      <motion.div
        className="fixed w-6 h-6 bg-cyan-400/20 rounded-full pointer-events-none z-50 mix-blend-difference"
        animate={{
          x: mousePosition.x - 12,
          y: mousePosition.y - 12,
        }}
        transition={{
          type: "spring",
          stiffness: 500,
          damping: 28,
        }}
      />

      {/* Header */}
      <motion.header
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="border-b border-slate-700/50 backdrop-blur-sm bg-slate-900/80 sticky top-0 z-40"
      >
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }} className="text-2xl font-bold">
              <NeonText>smmElsoury</NeonText>
            </motion.div>
            <nav className="hidden md:flex items-center space-x-8 space-x-reverse">
              {["الرئيسية", "الخدمات", "الدعم الفني", "الشروط والأحكام"].map((item, index) => (
                <motion.div
                  key={item}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 + 0.5 }}
                >
                  <Link
                    href={item === "الخدمات" ? "/services" : "/"}
                    className="text-white hover:text-cyan-400 transition-all duration-300 relative group"
                  >
                    {item}
                    <motion.div
                      className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-cyan-400 to-blue-500 group-hover:w-full transition-all duration-300"
                      whileHover={{ width: "100%" }}
                    />
                  </Link>
                </motion.div>
              ))}
            </nav>
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.8 }}
              className="flex items-center space-x-4 space-x-reverse"
            >
              {user ? (
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Link href="/dashboard">
                    <Button className="bg-gradient-to-r from-cyan-400 to-blue-500 text-slate-900 hover:from-cyan-500 hover:to-blue-600 transition-all duration-300 hover:shadow-[0_0_20px_rgba(6,182,212,0.5)]">
                      لوحة التحكم
                    </Button>
                  </Link>
                </motion.div>
              ) : (
                <>
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Link href="/auth/login">
                      <Button
                        variant="outline"
                        className="border-cyan-400 text-cyan-400 hover:bg-cyan-400 hover:text-slate-900 transition-all duration-300 hover:shadow-[0_0_20px_rgba(6,182,212,0.5)]"
                      >
                        تسجيل الدخول
                      </Button>
                    </Link>
                  </motion.div>
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Link href="/auth/register">
                      <Button className="bg-gradient-to-r from-cyan-400 to-blue-500 text-slate-900 hover:from-cyan-500 hover:to-blue-600 transition-all duration-300 hover:shadow-[0_0_20px_rgba(6,182,212,0.5)]">
                        إنشاء حساب
                      </Button>
                    </Link>
                  </motion.div>
                </>
              )}
            </motion.div>
          </div>
        </div>
      </motion.header>

      {/* Hero Section */}
      <section className="py-20 px-4 relative">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="container mx-auto text-center"
        >
          <motion.div variants={itemVariants}>
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
              أقوى لوحة{" "}
              <motion.span
                className="inline-block"
                animate={{
                  textShadow: [
                    "0 0 10px rgba(6,182,212,0.5)",
                    "0 0 20px rgba(6,182,212,0.8)",
                    "0 0 10px rgba(6,182,212,0.5)",
                  ],
                }}
                transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
              >
                <NeonText>SMM</NeonText>
              </motion.span>{" "}
              في العالم العربي
            </h1>
          </motion.div>

          <motion.div variants={itemVariants}>
            <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto leading-relaxed">
              خدمات التواصل الاجتماعي بأسعار منافسة وجودة عالية مع ضمان التنفيذ السريع
            </p>
          </motion.div>

          {/* Social Links */}
          <motion.div variants={itemVariants} className="mb-12">
            <div className="flex justify-center">
              <SocialLinks />
            </div>
          </motion.div>

          {/* Feature Cards */}
          <motion.div variants={containerVariants} className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
            {[
              { icon: Star, text: "جودة عالية", color: "from-pink-400 to-rose-600" },
              { icon: Clock, text: "تنفيذ فوري", color: "from-purple-400 to-indigo-600" },
              { icon: Shield, text: "أمان مضمون", color: "from-cyan-400 to-blue-600" },
              { icon: TrendingUp, text: "أسرع نمو", color: "from-green-400 to-emerald-600" },
            ].map((item, index) => (
              <motion.div key={index} variants={itemVariants}>
                <motion.div
                  whileHover={{
                    scale: 1.05,
                    rotateY: 5,
                    boxShadow: "0 20px 40px rgba(0,0,0,0.3)",
                  }}
                  whileTap={{ scale: 0.95 }}
                  className="group"
                >
                  <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm hover:border-cyan-400/50 transition-all duration-500 relative overflow-hidden">
                    <motion.div
                      className={`absolute inset-0 bg-gradient-to-r ${item.color} opacity-0 group-hover:opacity-10 transition-opacity duration-500`}
                    />
                    <CardContent className="p-6 text-center relative z-10">
                      <motion.div
                        animate={{
                          rotate: [0, 5, -5, 0],
                        }}
                        transition={{
                          duration: 4,
                          repeat: Number.POSITIVE_INFINITY,
                          ease: "easeInOut",
                        }}
                      >
                        <item.icon className="w-12 h-12 mx-auto mb-4 text-transparent bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text" />
                      </motion.div>
                      <h3 className="text-white font-semibold group-hover:text-cyan-400 transition-colors duration-300">
                        {item.text}
                      </h3>
                    </CardContent>
                  </Card>
                </motion.div>
              </motion.div>
            ))}
          </motion.div>

          {/* CTA Buttons */}
          <motion.div variants={itemVariants} className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
            <motion.div whileHover={{ scale: 1.05, y: -5 }} whileTap={{ scale: 0.95 }}>
              <Link href="/services">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-cyan-400 to-blue-500 text-slate-900 hover:from-cyan-500 hover:to-blue-600 px-8 py-3 text-lg font-semibold transition-all duration-300 hover:shadow-[0_0_30px_rgba(6,182,212,0.6)]"
                >
                  <Sparkles className="w-5 h-5 ml-2" />
                  استكشف الخدمات
                </Button>
              </Link>
            </motion.div>
            <motion.div whileHover={{ scale: 1.05, y: -5 }} whileTap={{ scale: 0.95 }}>
              <Link href={user ? "/dashboard" : "/auth/register"}>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-cyan-400 text-cyan-400 hover:bg-cyan-400 hover:text-slate-900 px-8 py-3 text-lg font-semibold transition-all duration-300 hover:shadow-[0_0_30px_rgba(6,182,212,0.4)]"
                >
                  {user ? "لوحة التحكم" : "ابدأ الآن مجاناً"} ←
                </Button>
              </Link>
            </motion.div>
          </motion.div>

          {/* Statistics */}
          <motion.div variants={containerVariants} className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              { value: 99.9, suffix: "%", label: "معدل النجاح" },
              { value: 100000, suffix: "+", label: "طلب مكتمل" },
              { value: 10000, suffix: "+", label: "عميل سعيد" },
            ].map((stat, index) => (
              <motion.div key={index} variants={itemVariants} whileHover={{ scale: 1.1 }} className="text-center group">
                <motion.div
                  className="text-4xl font-bold mb-2 bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent group-hover:from-cyan-300 group-hover:to-blue-400 transition-all duration-300"
                  animate={{
                    textShadow: [
                      "0 0 10px rgba(6,182,212,0.3)",
                      "0 0 20px rgba(6,182,212,0.6)",
                      "0 0 10px rgba(6,182,212,0.3)",
                    ],
                  }}
                  transition={{ duration: 3, repeat: Number.POSITIVE_INFINITY }}
                >
                  <AnimatedCounter end={stat.value} />
                  {stat.suffix}
                </motion.div>
                <div className="text-slate-300 group-hover:text-white transition-colors duration-300">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </section>

      {/* Services Section */}
      <section className="py-20 px-4 bg-slate-800/30 relative">
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="container mx-auto"
        >
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-white mb-4">
              <NeonText>خدماتنا المميزة</NeonText>
            </h2>
            <p className="text-xl text-slate-300">نقدم أفضل خدمات التسويق الرقمي بأسعار منافسة</p>
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            {[
              {
                title: "خدمات إنستغرام",
                description: "متابعين، إعجابات، مشاهدات، تعليقات",
                icon: "📸",
                price: "من 0.50$",
                gradient: "from-pink-500 to-rose-600",
              },
              {
                title: "خدمات فيسبوك",
                description: "صفحات، مجموعات، إعجابات، مشاركات",
                icon: "📘",
                price: "من 0.30$",
                gradient: "from-blue-500 to-indigo-600",
              },
              {
                title: "خدمات تيك توك",
                description: "متابعين، إعجابات، مشاهدات، مشاركات",
                icon: "🎵",
                price: "من 0.40$",
                gradient: "from-purple-500 to-pink-600",
              },
              {
                title: "خدمات يوتيوب",
                description: "مشتركين، مشاهدات، إعجابات، تعليقات",
                icon: "📺",
                price: "من 0.60$",
                gradient: "from-red-500 to-orange-600",
              },
              {
                title: "خدمات تويتر",
                description: "متابعين، إعجابات، ريتويت، تعليقات",
                icon: "🐦",
                price: "من 0.45$",
                gradient: "from-cyan-500 to-blue-600",
              },
              {
                title: "خدمات سناب شات",
                description: "متابعين، مشاهدات، نقاط سناب",
                icon: "👻",
                price: "من 0.55$",
                gradient: "from-yellow-500 to-orange-600",
              },
            ].map((service, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                whileHover={{
                  scale: 1.05,
                  rotateY: 10,
                  z: 50,
                }}
                className="group perspective-1000"
              >
                <Card className="bg-slate-800/50 border-slate-700 hover:border-cyan-400/50 transition-all duration-500 relative overflow-hidden backdrop-blur-sm">
                  <motion.div
                    className={`absolute inset-0 bg-gradient-to-r ${service.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-500`}
                  />
                  <motion.div
                    className="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-r from-cyan-400/20 to-blue-600/20 rounded-full blur-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                    animate={{
                      rotate: 360,
                    }}
                    transition={{
                      duration: 20,
                      repeat: Number.POSITIVE_INFINITY,
                      ease: "linear",
                    }}
                  />
                  <CardContent className="p-6 relative z-10">
                    <motion.div
                      className="text-4xl mb-4"
                      animate={{
                        scale: [1, 1.1, 1],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Number.POSITIVE_INFINITY,
                        ease: "easeInOut",
                      }}
                    >
                      {service.icon}
                    </motion.div>
                    <h3 className="text-xl font-bold text-white mb-2 group-hover:text-cyan-400 transition-colors duration-300">
                      {service.title}
                    </h3>
                    <p className="text-slate-300 mb-4 group-hover:text-slate-200 transition-colors duration-300">
                      {service.description}
                    </p>
                    <div className="flex justify-between items-center">
                      <span className="text-cyan-400 font-bold text-lg group-hover:text-cyan-300 transition-colors duration-300">
                        {service.price}
                      </span>
                      <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                        <Link href={user ? "/dashboard" : "/auth/login"}>
                          <Button
                            size="sm"
                            className="bg-gradient-to-r from-cyan-400 to-blue-500 text-slate-900 hover:from-cyan-500 hover:to-blue-600 transition-all duration-300 hover:shadow-[0_0_20px_rgba(6,182,212,0.5)]"
                          >
                            اطلب الآن
                          </Button>
                        </Link>
                      </motion.div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 relative">
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="container mx-auto"
        >
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-white mb-4">
              <NeonText>لماذا تختار smmElsoury؟</NeonText>
            </h2>
            <p className="text-xl text-slate-300">نحن الخيار الأول للمسوقين المحترفين</p>
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {[
              {
                icon: Zap,
                title: "سرعة في التنفيذ",
                description: "نبدأ تنفيذ طلبك خلال دقائق من الطلب",
                color: "text-yellow-400",
              },
              {
                icon: Shield,
                title: "أمان وحماية",
                description: "حماية كاملة لحسابك وبياناتك الشخصية",
                color: "text-green-400",
              },
              {
                icon: Users,
                title: "دعم فني 24/7",
                description: "فريق دعم متاح على مدار الساعة لمساعدتك",
                color: "text-blue-400",
              },
              {
                icon: CheckCircle,
                title: "ضمان الجودة",
                description: "ضمان استرداد الأموال في حالة عدم الرضا",
                color: "text-cyan-400",
              },
              {
                icon: TrendingUp,
                title: "نتائج حقيقية",
                description: "جميع الخدمات من حسابات حقيقية ونشطة",
                color: "text-purple-400",
              },
              {
                icon: Star,
                title: "أسعار منافسة",
                description: "أفضل الأسعار في السوق مع جودة عالية",
                color: "text-pink-400",
              },
            ].map((feature, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                whileHover={{
                  scale: 1.05,
                  y: -10,
                }}
                className="group"
              >
                <Card className="bg-slate-800/50 border-slate-700 hover:border-cyan-400/50 transition-all duration-500 relative overflow-hidden backdrop-blur-sm h-full">
                  <motion.div className="absolute inset-0 bg-gradient-to-r from-cyan-400/5 to-blue-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                  <CardContent className="p-6 text-center relative z-10 h-full flex flex-col justify-center">
                    <motion.div
                      className="mb-4"
                      animate={{
                        y: [0, -10, 0],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Number.POSITIVE_INFINITY,
                        ease: "easeInOut",
                      }}
                    >
                      <feature.icon className={`w-8 h-8 ${feature.color} mx-auto`} />
                    </motion.div>
                    <h3 className="text-xl font-bold text-white mb-2 group-hover:text-cyan-400 transition-colors duration-300">
                      {feature.title}
                    </h3>
                    <p className="text-slate-300 group-hover:text-slate-200 transition-colors duration-300">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </section>

      {/* CTA Section */}
      <motion.section
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
        className="py-20 px-4 bg-gradient-to-r from-cyan-600 to-blue-600 relative overflow-hidden"
      >
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-cyan-400/20 to-blue-600/20"
          animate={{
            backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
          }}
          transition={{
            duration: 10,
            repeat: Number.POSITIVE_INFINITY,
            ease: "linear",
          }}
        />
        <div className="container mx-auto text-center relative z-10">
          <motion.h2
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-4xl font-bold text-white mb-4"
          >
            ابدأ رحلتك معنا اليوم
          </motion.h2>
          <motion.p
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-xl text-cyan-100 mb-8"
          >
            انضم إلى آلاف العملاء الراضين واحصل على أفضل خدمات SMM
          </motion.p>
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <motion.div whileHover={{ scale: 1.05, y: -5 }} whileTap={{ scale: 0.95 }}>
              <Link href={user ? "/dashboard" : "/auth/register"}>
                <Button
                  size="lg"
                  className="bg-white text-cyan-600 hover:bg-slate-100 px-8 py-3 text-lg font-semibold transition-all duration-300 hover:shadow-[0_0_30px_rgba(255,255,255,0.3)]"
                >
                  {user ? "لوحة التحكم" : "إنشاء حساب مجاني"}
                </Button>
              </Link>
            </motion.div>
            <motion.div whileHover={{ scale: 1.05, y: -5 }} whileTap={{ scale: 0.95 }}>
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-cyan-600 px-8 py-3 text-lg font-semibold transition-all duration-300 hover:shadow-[0_0_30px_rgba(255,255,255,0.2)]"
              >
                تواصل معنا
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </motion.section>

      {/* Footer */}
      <footer className="bg-slate-900 py-12 px-4 relative">
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="container mx-auto"
        >
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <motion.div
              initial={{ y: 50, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="text-2xl font-bold mb-4">
                <NeonText>smmElsoury</NeonText>
              </div>
              <p className="text-slate-300 mb-4">أقوى لوحة SMM في العالم العربي</p>
              <SocialLinks />
            </motion.div>
            {[
              {
                title: "روابط سريعة",
                links: [
                  { name: "الرئيسية", href: "/" },
                  { name: "الخدمات", href: "/services" },
                  { name: "الأسعار", href: "/pricing" },
                  { name: "الدعم", href: "/support" },
                ],
              },
              {
                title: "الخدمات",
                links: [
                  { name: "إنستغرام", href: "/services" },
                  { name: "فيسبوك", href: "/services" },
                  { name: "تيك توك", href: "/services" },
                  { name: "يوتيوب", href: "/services" },
                ],
              },
              {
                title: "تواصل معنا",
                links: [
                  { name: "البريد الإلكتروني: <EMAIL>", href: "mailto:<EMAIL>" },
                  { name: "الهاتف: +966 50 123 4567", href: "tel:+966501234567" },
                  { name: "الواتساب: +966 50 123 4567", href: "https://wa.me/966501234567" },
                ],
              },
            ].map((section, index) => (
              <motion.div
                key={index}
                initial={{ y: 50, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <h3 className="text-white font-bold mb-4">{section.title}</h3>
                <ul className="space-y-2 text-slate-300">
                  {section.links.map((link, linkIndex) => (
                    <motion.li
                      key={linkIndex}
                      whileHover={{ x: 5, color: "#06b6d4" }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <Link href={link.href} className="hover:text-cyan-400 transition-colors duration-300">
                        {link.name}
                      </Link>
                    </motion.li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            viewport={{ once: true }}
            className="border-t border-slate-700 mt-8 pt-8 text-center text-slate-300"
          >
            <p>&copy; 2024 smmElsoury. جميع الحقوق محفوظة.</p>
          </motion.div>
        </motion.div>
      </footer>
    </div>
  )
}
