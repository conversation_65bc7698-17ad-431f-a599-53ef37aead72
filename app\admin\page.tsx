"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Users,
  ShoppingCart,
  DollarSign,
  TrendingUp,
  Settings,
  UserPlus,
  Edit,
  Trash2,
  Eye,
  Plus,
  Search,
  Shield,
  Activity,
  AlertCircle,
  CheckCircle,
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { useAuth } from "@/lib/auth"
import { useRouter } from "next/navigation"
import SocialLinks from "@/components/social-links"

export default function AdminDashboard() {
  const { user, logout } = useAuth()
  const router = useRouter()
  const [notification, setNotification] = useState<{ type: "success" | "error"; message: string } | null>(null)
  const [stats] = useState({
    totalUsers: 1250,
    totalOrders: 5680,
    totalRevenue: 45230.5,
    activeServices: 24,
  })
  const [users] = useState([
    { id: 1, name: "أحمد محمد", email: "<EMAIL>", balance: 150.75, orders: 12, status: "نشط" },
    { id: 2, name: "فاطمة علي", email: "<EMAIL>", balance: 89.25, orders: 8, status: "نشط" },
    { id: 3, name: "محمد حسن", email: "<EMAIL>", balance: 0.0, orders: 3, status: "معلق" },
  ])
  const [orders] = useState([
    { id: "ORD-001", user: "أحمد محمد", service: "متابعين إنستغرام", quantity: 1000, price: 15.5, status: "مكتمل" },
    { id: "ORD-002", user: "فاطمة علي", service: "إعجابات فيسبوك", quantity: 500, price: 8.25, status: "قيد التنفيذ" },
    { id: "ORD-003", user: "محمد حسن", service: "مشاهدات يوتيوب", quantity: 2000, price: 12.0, status: "معلق" },
  ])
  const [services] = useState([
    { id: 1, name: "متابعين إنستغرام", category: "إنستغرام", price: 0.5, status: "نشط" },
    { id: 2, name: "إعجابات فيسبوك", category: "فيسبوك", price: 0.3, status: "نشط" },
    { id: 3, name: "مشاهدات يوتيوب", category: "يوتيوب", price: 0.6, status: "متوقف" },
  ])

  // Redirect if not authenticated or not admin
  useEffect(() => {
    if (!user) {
      router.push("/auth/login")
    } else if (user.role !== "admin") {
      router.push("/dashboard")
    }
  }, [user, router])

  const getStatusColor = (status: string) => {
    switch (status) {
      case "نشط":
      case "مكتمل":
        return "bg-green-500"
      case "قيد التنفيذ":
        return "bg-yellow-500"
      case "معلق":
      case "متوقف":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  const handleLogout = () => {
    logout()
    router.push("/")
  }

  const showNotification = (type: "success" | "error", message: string) => {
    setNotification({ type, message })
    setTimeout(() => setNotification(null), 5000)
  }

  if (!user || user.role !== "admin") {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
          className="w-8 h-8 border-2 border-cyan-400 border-t-transparent rounded-full"
        />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative" dir="rtl">
      {/* Animated Background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <motion.div
          className="absolute top-20 right-20 w-60 h-60 bg-gradient-to-r from-red-400/10 to-orange-600/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-r from-purple-400/10 to-pink-600/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.2, 0.5, 0.2],
          }}
          transition={{
            duration: 12,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
        />
      </div>

      {/* Notification */}
      <AnimatePresence>
        {notification && (
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            className="fixed top-4 right-4 z-50"
          >
            <Alert
              className={`${notification.type === "success" ? "border-green-500/50 bg-green-500/10" : "border-red-500/50 bg-red-500/10"}`}
            >
              {notification.type === "success" ? (
                <CheckCircle className="h-4 w-4 text-green-400" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-400" />
              )}
              <AlertDescription className={notification.type === "success" ? "text-green-400" : "text-red-400"}>
                {notification.message}
              </AlertDescription>
            </Alert>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Header */}
      <motion.header
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8 }}
        className="border-b border-slate-700/50 backdrop-blur-sm bg-slate-900/80 relative z-10"
      >
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 space-x-reverse">
              <motion.div whileHover={{ scale: 1.05 }} className="text-2xl font-bold">
                <span className="bg-gradient-to-r from-red-400 to-orange-500 bg-clip-text text-transparent">
                  smmElsoury
                </span>
              </motion.div>
              <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} transition={{ delay: 0.3, type: "spring" }}>
                <Badge variant="secondary" className="bg-red-500/20 text-red-400 border-red-500/30">
                  <Shield className="w-3 h-3 ml-1" />
                  لوحة الإدارة
                </Badge>
              </motion.div>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 }}
                className="flex items-center space-x-2 space-x-reverse"
              >
                <div className="text-right">
                  <div className="text-white font-medium">مرحباً، {user.name}</div>
                  <div className="text-slate-400 text-sm">مدير النظام</div>
                </div>
              </motion.div>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-slate-600 text-slate-300 hover:border-cyan-400 hover:text-cyan-400"
                >
                  <Settings className="w-4 h-4 ml-2" />
                  الإعدادات
                </Button>
              </motion.div>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleLogout}
                  className="border-red-600 text-red-400 hover:bg-red-600 hover:text-white"
                >
                  تسجيل الخروج
                </Button>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.header>

      <div className="container mx-auto px-4 py-8 relative z-10">
        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
        >
          {[
            {
              title: "إجمالي المستخدمين",
              value: stats.totalUsers.toLocaleString(),
              icon: Users,
              color: "from-blue-400 to-cyan-600",
              bgColor: "bg-blue-400/10",
            },
            {
              title: "إجمالي الطلبات",
              value: stats.totalOrders.toLocaleString(),
              icon: ShoppingCart,
              color: "from-green-400 to-emerald-600",
              bgColor: "bg-green-400/10",
            },
            {
              title: "إجمالي الإيرادات",
              value: `$${stats.totalRevenue.toLocaleString()}`,
              icon: DollarSign,
              color: "from-yellow-400 to-orange-600",
              bgColor: "bg-yellow-400/10",
            },
            {
              title: "الخدمات النشطة",
              value: stats.activeServices.toString(),
              icon: TrendingUp,
              color: "from-purple-400 to-pink-600",
              bgColor: "bg-purple-400/10",
            },
          ].map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 + 0.3 }}
              whileHover={{ scale: 1.05, y: -5 }}
              className="group"
            >
              <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm hover:border-red-400/50 transition-all duration-500 relative overflow-hidden">
                <motion.div
                  className={`absolute inset-0 ${stat.bgColor} opacity-0 group-hover:opacity-100 transition-opacity duration-500`}
                />
                <CardContent className="p-6 relative z-10">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-slate-300 text-sm mb-1">{stat.title}</p>
                      <p className="text-2xl font-bold text-white">{stat.value}</p>
                    </div>
                    <motion.div
                      animate={{
                        rotate: [0, 5, -5, 0],
                      }}
                      transition={{
                        duration: 4,
                        repeat: Number.POSITIVE_INFINITY,
                        ease: "easeInOut",
                      }}
                    >
                      <stat.icon className={`w-8 h-8 bg-gradient-to-r ${stat.color} bg-clip-text text-transparent`} />
                    </motion.div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Social Links Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mb-8"
        >
          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Activity className="w-5 h-5 text-red-400" />
                إدارة وسائل التواصل الاجتماعي
              </CardTitle>
              <CardDescription className="text-slate-300">روابط الحسابات الرسمية للموقع</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-center">
                <SocialLinks />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Main Content */}
        <motion.div initial={{ opacity: 0, y: 50 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.8 }}>
          <Tabs defaultValue="users" className="space-y-6">
            <TabsList className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
              {["users", "orders", "services", "settings"].map((tab, index) => (
                <motion.div
                  key={tab}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 + 1 }}
                >
                  <TabsTrigger
                    value={tab}
                    className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-red-400 data-[state=active]:to-orange-500 data-[state=active]:text-slate-900 transition-all duration-300"
                  >
                    {tab === "users" && "المستخدمين"}
                    {tab === "orders" && "الطلبات"}
                    {tab === "services" && "الخدمات"}
                    {tab === "settings" && "الإعدادات"}
                  </TabsTrigger>
                </motion.div>
              ))}
            </TabsList>

            <AnimatePresence mode="wait">
              <TabsContent value="users">
                <motion.div
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 50 }}
                  transition={{ duration: 0.5 }}
                >
                  <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="text-white">إدارة المستخدمين</CardTitle>
                          <CardDescription className="text-slate-300">عرض وإدارة جميع المستخدمين</CardDescription>
                        </div>
                        <div className="flex items-center space-x-4 space-x-reverse">
                          <div className="relative">
                            <Search className="absolute right-3 top-3 h-4 w-4 text-slate-400" />
                            <Input
                              placeholder="البحث عن مستخدم..."
                              className="pr-10 bg-slate-700/50 border-slate-600 text-white"
                            />
                          </div>
                          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                            <Button
                              onClick={() => showNotification("success", "تم إضافة مستخدم جديد بنجاح")}
                              className="bg-gradient-to-r from-red-400 to-orange-500 text-slate-900 hover:from-red-500 hover:to-orange-600"
                            >
                              <UserPlus className="w-4 h-4 ml-2" />
                              إضافة مستخدم
                            </Button>
                          </motion.div>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {users.map((user, index) => (
                          <motion.div
                            key={user.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 }}
                            whileHover={{ scale: 1.02, x: 5 }}
                            className="group"
                          >
                            <div className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg hover:bg-slate-700/50 transition-all duration-300 relative overflow-hidden">
                              <motion.div className="absolute inset-0 bg-gradient-to-r from-red-400/5 to-orange-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                              <div className="flex-1 relative z-10">
                                <div className="flex items-center space-x-4 space-x-reverse">
                                  <div>
                                    <p className="text-white font-medium group-hover:text-red-400 transition-colors duration-300">
                                      {user.name}
                                    </p>
                                    <p className="text-slate-300 text-sm">{user.email}</p>
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center space-x-4 space-x-reverse relative z-10">
                                <div className="text-center">
                                  <p className="text-white font-medium">${user.balance}</p>
                                  <p className="text-slate-300 text-sm">الرصيد</p>
                                </div>
                                <div className="text-center">
                                  <p className="text-white font-medium">{user.orders}</p>
                                  <p className="text-slate-300 text-sm">الطلبات</p>
                                </div>
                                <Badge className={getStatusColor(user.status)}>{user.status}</Badge>
                                <div className="flex space-x-2 space-x-reverse">
                                  <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                                    <Button size="sm" variant="outline" className="border-slate-600">
                                      <Eye className="w-4 h-4" />
                                    </Button>
                                  </motion.div>
                                  <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                                    <Button size="sm" variant="outline" className="border-slate-600">
                                      <Edit className="w-4 h-4" />
                                    </Button>
                                  </motion.div>
                                  <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                                    <Button size="sm" variant="outline" className="border-red-600 text-red-400">
                                      <Trash2 className="w-4 h-4" />
                                    </Button>
                                  </motion.div>
                                </div>
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </TabsContent>

              <TabsContent value="orders">
                <motion.div
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 50 }}
                  transition={{ duration: 0.5 }}
                >
                  <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="text-white">إدارة الطلبات</CardTitle>
                          <CardDescription className="text-slate-300">عرض وإدارة جميع الطلبات</CardDescription>
                        </div>
                        <div className="relative">
                          <Search className="absolute right-3 top-3 h-4 w-4 text-slate-400" />
                          <Input
                            placeholder="البحث عن طلب..."
                            className="pr-10 bg-slate-700/50 border-slate-600 text-white"
                          />
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {orders.map((order, index) => (
                          <motion.div
                            key={order.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 }}
                            whileHover={{ scale: 1.02, x: 5 }}
                            className="group"
                          >
                            <div className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg hover:bg-slate-700/50 transition-all duration-300 relative overflow-hidden">
                              <motion.div className="absolute inset-0 bg-gradient-to-r from-red-400/5 to-orange-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                              <div className="flex-1 relative z-10">
                                <div className="flex items-center space-x-4 space-x-reverse">
                                  <div>
                                    <p className="text-white font-medium group-hover:text-red-400 transition-colors duration-300">
                                      {order.id}
                                    </p>
                                    <p className="text-slate-300 text-sm">{order.user}</p>
                                  </div>
                                  <div>
                                    <p className="text-white">{order.service}</p>
                                    <p className="text-slate-300 text-sm">الكمية: {order.quantity.toLocaleString()}</p>
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center space-x-4 space-x-reverse relative z-10">
                                <div className="text-center">
                                  <p className="text-white font-medium">${order.price}</p>
                                  <p className="text-slate-300 text-sm">السعر</p>
                                </div>
                                <Badge className={getStatusColor(order.status)}>{order.status}</Badge>
                                <div className="flex space-x-2 space-x-reverse">
                                  <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                                    <Button size="sm" variant="outline" className="border-slate-600">
                                      <Eye className="w-4 h-4" />
                                    </Button>
                                  </motion.div>
                                  <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => showNotification("success", "تم تحديث حالة الطلب بنجاح")}
                                      className="border-slate-600"
                                    >
                                      <Edit className="w-4 h-4" />
                                    </Button>
                                  </motion.div>
                                </div>
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </TabsContent>

              <TabsContent value="services">
                <motion.div
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 50 }}
                  transition={{ duration: 0.5 }}
                >
                  <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="text-white">إدارة الخدمات</CardTitle>
                          <CardDescription className="text-slate-300">إضافة وتعديل الخدمات المتاحة</CardDescription>
                        </div>
                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                          <Button
                            onClick={() => showNotification("success", "تم إضافة خدمة جديدة بنجاح")}
                            className="bg-gradient-to-r from-red-400 to-orange-500 text-slate-900 hover:from-red-500 hover:to-orange-600"
                          >
                            <Plus className="w-4 h-4 ml-2" />
                            إضافة خدمة
                          </Button>
                        </motion.div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {services.map((service, index) => (
                          <motion.div
                            key={service.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 }}
                            whileHover={{ scale: 1.02, x: 5 }}
                            className="group"
                          >
                            <div className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg hover:bg-slate-700/50 transition-all duration-300 relative overflow-hidden">
                              <motion.div className="absolute inset-0 bg-gradient-to-r from-red-400/5 to-orange-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                              <div className="flex-1 relative z-10">
                                <div className="flex items-center space-x-4 space-x-reverse">
                                  <div>
                                    <p className="text-white font-medium group-hover:text-red-400 transition-colors duration-300">
                                      {service.name}
                                    </p>
                                    <p className="text-slate-300 text-sm">{service.category}</p>
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center space-x-4 space-x-reverse relative z-10">
                                <div className="text-center">
                                  <p className="text-white font-medium">${service.price}</p>
                                  <p className="text-slate-300 text-sm">لكل 1000</p>
                                </div>
                                <Badge className={getStatusColor(service.status)}>{service.status}</Badge>
                                <div className="flex space-x-2 space-x-reverse">
                                  <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => showNotification("success", "تم تحديث الخدمة بنجاح")}
                                      className="border-slate-600"
                                    >
                                      <Edit className="w-4 h-4" />
                                    </Button>
                                  </motion.div>
                                  <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                                    <Button size="sm" variant="outline" className="border-red-600 text-red-400">
                                      <Trash2 className="w-4 h-4" />
                                    </Button>
                                  </motion.div>
                                </div>
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </TabsContent>

              <TabsContent value="settings">
                <motion.div
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 50 }}
                  transition={{ duration: 0.5 }}
                  className="grid grid-cols-1 md:grid-cols-2 gap-6"
                >
                  <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                    <CardHeader>
                      <CardTitle className="text-white">إعدادات النظام</CardTitle>
                      <CardDescription className="text-slate-300">تكوين الإعدادات العامة للنظام</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="site-name" className="text-white">
                          اسم الموقع
                        </Label>
                        <Input
                          id="site-name"
                          defaultValue="smmElsoury"
                          className="bg-slate-700/50 border-slate-600 text-white"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="admin-email" className="text-white">
                          بريد المدير
                        </Label>
                        <Input
                          id="admin-email"
                          type="email"
                          defaultValue="<EMAIL>"
                          className="bg-slate-700/50 border-slate-600 text-white"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="currency" className="text-white">
                          العملة الافتراضية
                        </Label>
                        <Input
                          id="currency"
                          defaultValue="USD"
                          className="bg-slate-700/50 border-slate-600 text-white"
                        />
                      </div>
                      <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                        <Button
                          onClick={() => showNotification("success", "تم حفظ الإعدادات بنجاح")}
                          className="w-full bg-gradient-to-r from-red-400 to-orange-500 text-slate-900 hover:from-red-500 hover:to-orange-600"
                        >
                          حفظ الإعدادات
                        </Button>
                      </motion.div>
                    </CardContent>
                  </Card>

                  <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                    <CardHeader>
                      <CardTitle className="text-white">إعدادات الدفع</CardTitle>
                      <CardDescription className="text-slate-300">تكوين طرق الدفع المتاحة</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="vodafone-cash" className="text-white">
                          رقم فودافون كاش
                        </Label>
                        <Input
                          id="vodafone-cash"
                          defaultValue="01011016735"
                          className="bg-slate-700/50 border-slate-600 text-white"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="instapay" className="text-white">
                          رقم انستاباي
                        </Label>
                        <Input
                          id="instapay"
                          defaultValue="01011016735"
                          className="bg-slate-700/50 border-slate-600 text-white"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="min-deposit" className="text-white">
                          أقل مبلغ إيداع
                        </Label>
                        <Input
                          id="min-deposit"
                          type="number"
                          defaultValue="10"
                          className="bg-slate-700/50 border-slate-600 text-white"
                        />
                      </div>
                      <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                        <Button
                          onClick={() => showNotification("success", "تم حفظ إعدادات الدفع بنجاح")}
                          className="w-full bg-gradient-to-r from-red-400 to-orange-500 text-slate-900 hover:from-red-500 hover:to-orange-600"
                        >
                          حفظ إعدادات الدفع
                        </Button>
                      </motion.div>
                    </CardContent>
                  </Card>
                </motion.div>
              </TabsContent>
            </AnimatePresence>
          </Tabs>
        </motion.div>
      </div>
    </div>
  )
}
