"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Star } from "lucide-react"
import type { Service } from "@/lib/types"
import { useRouter } from "next/navigation"

interface ServiceCardProps {
  service: Service
  onOrderClick: (service: Service) => void
}

export default function ServiceCard({ service, onOrderClick }: ServiceCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const router = useRouter()

  return (
    <motion.div
      whileHover={{
        scale: 1.05,
        rotateY: 10,
        z: 50,
      }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      className="group perspective-1000"
    >
      <Card className="bg-slate-800/50 border-slate-700 hover:border-cyan-400/50 transition-all duration-500 relative overflow-hidden backdrop-blur-sm h-full">
        <motion.div
          className={`absolute inset-0 bg-gradient-to-r from-${service.category === "instagram" ? "pink-500 to-rose-600" : service.category === "facebook" ? "blue-500 to-indigo-600" : service.category === "youtube" ? "red-500 to-orange-600" : service.category === "tiktok" ? "purple-500 to-pink-600" : service.category === "twitter" ? "cyan-500 to-blue-600" : "yellow-500 to-orange-600"} opacity-0 group-hover:opacity-10 transition-opacity duration-500`}
        />
        <motion.div
          className="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-r from-cyan-400/20 to-blue-600/20 rounded-full blur-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
          animate={{
            rotate: 360,
          }}
          transition={{
            duration: 20,
            repeat: Number.POSITIVE_INFINITY,
            ease: "linear",
          }}
        />
        <CardContent className="p-6 relative z-10 flex flex-col h-full">
          <motion.div
            className="text-4xl mb-4"
            animate={{
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration: 2,
              repeat: Number.POSITIVE_INFINITY,
              ease: "easeInOut",
            }}
          >
            {service.category === "instagram"
              ? "📸"
              : service.category === "facebook"
                ? "📘"
                : service.category === "youtube"
                  ? "📺"
                  : service.category === "tiktok"
                    ? "🎵"
                    : service.category === "twitter"
                      ? "🐦"
                      : "👻"}
          </motion.div>
          <h3 className="text-xl font-bold text-white mb-2 group-hover:text-cyan-400 transition-colors duration-300">
            {service.name}
          </h3>
          <p className="text-slate-300 mb-4 group-hover:text-slate-200 transition-colors duration-300">
            {service.description}
          </p>

          {/* Features */}
          <div className="flex flex-wrap gap-2 mb-4">
            {service.features.map((feature, index) => (
              <Badge key={index} variant="secondary" className="bg-slate-700 text-slate-300">
                {feature}
              </Badge>
            ))}
          </div>

          {/* Stats */}
          <div className="flex items-center justify-between text-sm mt-auto mb-4">
            <div className="flex items-center space-x-1 space-x-reverse text-yellow-400">
              <Star className="w-4 h-4 fill-current" />
              <span>{service.rating}</span>
            </div>
            <div className="text-slate-300">{service.orders?.toLocaleString()} طلب</div>
          </div>

          {/* Order Info */}
          <div className="space-y-2 text-sm text-slate-300 mb-4">
            <div className="flex justify-between">
              <span>السعر لكل 1000:</span>
              <span className="text-cyan-400 font-bold">${service.price}</span>
            </div>
            <div className="flex justify-between">
              <span>أقل طلب:</span>
              <span>{service.minOrder.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span>أكبر طلب:</span>
              <span>{service.maxOrder.toLocaleString()}</span>
            </div>
          </div>

          <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
            <Button
              onClick={() => onOrderClick(service)}
              className="w-full bg-gradient-to-r from-cyan-400 to-blue-500 text-slate-900 hover:from-cyan-500 hover:to-blue-600 transition-all duration-300 hover:shadow-[0_0_20px_rgba(6,182,212,0.5)]"
            >
              اطلب الآن
            </Button>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
