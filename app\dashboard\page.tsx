"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Input } from "@/components/ui/input"
import {
  DollarSign,
  ShoppingCart,
  Clock,
  CheckCircle,
  Plus,
  Eye,
  Settings,
  LogOut,
  TrendingUp,
  Zap,
  Activity,
  AlertCircle,
  User,
  Search,
  Filter,
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { useAuth } from "@/lib/auth"
import { useRouter } from "next/navigation"
import PaymentMethods from "@/components/payment-methods"
import SocialLinks from "@/components/social-links"
import Notifications from "@/components/notifications"
import ServiceCard from "@/components/service-card"
import OrderModal from "@/components/order-modal"
import { services, categories } from "@/lib/data"
import type { Service } from "@/lib/types"
import { format } from "date-fns"
import { arSA } from "date-fns/locale"

// Animated Background for Dashboard
const DashboardBackground = () => {
  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none">
      <motion.div
        className="absolute top-20 right-20 w-60 h-60 bg-gradient-to-r from-cyan-400/10 to-blue-600/10 rounded-full blur-3xl"
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.6, 0.3],
        }}
        transition={{
          duration: 8,
          repeat: Number.POSITIVE_INFINITY,
          ease: "easeInOut",
        }}
      />
      <motion.div
        className="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-r from-purple-400/10 to-pink-600/10 rounded-full blur-3xl"
        animate={{
          scale: [1, 1.1, 1],
          opacity: [0.2, 0.5, 0.2],
        }}
        transition={{
          duration: 12,
          repeat: Number.POSITIVE_INFINITY,
          ease: "easeInOut",
        }}
      />
    </div>
  )
}

// Animated Counter Component
const AnimatedCounter = ({
  end,
  duration = 2,
  prefix = "",
  suffix = "",
}: { end: number; duration?: number; prefix?: string; suffix?: string }) => {
  const [count, setCount] = useState(0)

  useEffect(() => {
    const timer = setInterval(() => {
      setCount((prev) => {
        if (prev < end) {
          return Math.min(prev + Math.ceil(end / (duration * 60)), end)
        }
        clearInterval(timer)
        return end
      })
    }, 1000 / 60)

    return () => clearInterval(timer)
  }, [end, duration])

  return (
    <span>
      {prefix}
      {count.toLocaleString()}
      {suffix}
    </span>
  )
}

export default function DashboardPage() {
  const { user, logout, orders, transactions, addOrder, addTransaction, addNotification } = useAuth()
  const router = useRouter()
  const [showPayment, setShowPayment] = useState(false)
  const [paymentAmount, setPaymentAmount] = useState(0)
  const [notification, setNotification] = useState<{ type: "success" | "error"; message: string } | null>(null)
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedService, setSelectedService] = useState<Service | null>(null)
  const [showOrderModal, setShowOrderModal] = useState(false)

  const getStatusColor = (status: string) => {
    switch (status) {
      case "مكتمل":
        return "bg-green-500"
      case "قيد التنفيذ":
        return "bg-yellow-500"
      case "معلق":
        return "bg-blue-500"
      case "ملغي":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  const handleLogout = () => {
    logout()
    router.push("/")
  }

  const handlePaymentComplete = (method: string, transactionId: string) => {
    if (user) {
      addTransaction({
        type: "شحن",
        amount: paymentAmount,
        description: `شحن رصيد عبر ${method}`,
        status: "معلق",
        paymentMethod: method,
        paymentId: transactionId,
      })

      setNotification({
        type: "success",
        message: `تم إرسال طلب الدفع بنجاح! رقم العملية: ${transactionId}. سيتم مراجعة الطلب وإضافة الرصيد خلال 24 ساعة.`,
      })

      addNotification({
        title: "تم إرسال طلب شحن الرصيد",
        message: `تم إرسال طلب شحن رصيد بقيمة $${paymentAmount} عبر ${method}. سيتم مراجعة الطلب قريبًا.`,
        type: "info",
      })
    }

    setShowPayment(false)
    setTimeout(() => setNotification(null), 5000)
  }

  const handleChargeBalance = (amount: number) => {
    setPaymentAmount(amount)
    setShowPayment(true)
  }

  const handleServiceOrder = (service: Service) => {
    setSelectedService(service)
    setShowOrderModal(true)
  }

  const handleOrderSubmit = async (orderData: {
    serviceId: number
    serviceName: string
    quantity: number
    price: number
    link: string
  }) => {
    if (!user) return

    if (user.balance < orderData.price) {
      setNotification({
        type: "error",
        message: "رصيدك غير كافي لإتمام هذا الطلب. يرجى شحن رصيدك أولاً.",
      })
      setTimeout(() => setNotification(null), 5000)
      return
    }

    const success = await addOrder(orderData)
    if (success) {
      setNotification({
        type: "success",
        message: "تم إنشاء الطلب بنجاح! سيتم تنفيذه قريبًا.",
      })
      setShowOrderModal(false)
      setSelectedService(null)
    } else {
      setNotification({
        type: "error",
        message: "حدث خطأ أثناء إنشاء الطلب. يرجى المحاولة مرة أخرى.",
      })
    }
    setTimeout(() => setNotification(null), 5000)
  }

  const filteredServices = services.filter((service) => {
    const matchesSearch = service.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "all" || service.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
      },
    },
  }

  // Redirect if not authenticated
  useEffect(() => {
    if (!user) {
      router.push("/auth/login")
    }
  }, [user, router])

  // Redirect admin to admin dashboard
  useEffect(() => {
    if (user?.role === "admin") {
      router.push("/admin")
    }
  }, [user, router])

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
          className="w-8 h-8 border-2 border-cyan-400 border-t-transparent rounded-full"
        />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative" dir="rtl">
      <DashboardBackground />

      {/* Notification */}
      <AnimatePresence>
        {notification && (
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            className="fixed top-4 right-4 z-50"
          >
            <Alert
              className={`${notification.type === "success" ? "border-green-500/50 bg-green-500/10" : "border-red-500/50 bg-red-500/10"}`}
            >
              {notification.type === "success" ? (
                <CheckCircle className="h-4 w-4 text-green-400" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-400" />
              )}
              <AlertDescription className={notification.type === "success" ? "text-green-400" : "text-red-400"}>
                {notification.message}
              </AlertDescription>
            </Alert>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Payment Modal */}
      <AnimatePresence>
        {showPayment && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 flex items-center justify-center p-4"
            onClick={() => setShowPayment(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 max-w-md w-full max-h-[90vh] overflow-y-auto"
            >
              <PaymentMethods amount={paymentAmount} onPaymentComplete={handlePaymentComplete} />
              <Button
                variant="outline"
                onClick={() => setShowPayment(false)}
                className="w-full mt-4 border-slate-600 text-slate-300"
              >
                إلغاء
              </Button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Order Modal */}
      <AnimatePresence>
        {showOrderModal && (
          <OrderModal
            service={selectedService}
            onClose={() => {
              setShowOrderModal(false)
              setSelectedService(null)
            }}
            onSubmit={handleOrderSubmit}
          />
        )}
      </AnimatePresence>

      {/* Header */}
      <motion.header
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8 }}
        className="border-b border-slate-700/50 backdrop-blur-sm bg-slate-900/80 relative z-10"
      >
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 space-x-reverse">
              <motion.div whileHover={{ scale: 1.05 }} className="text-2xl font-bold">
                <span className="bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent">
                  smmElsoury
                </span>
              </motion.div>
              <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} transition={{ delay: 0.3, type: "spring" }}>
                <Badge variant="secondary" className="bg-cyan-400/20 text-cyan-400 border-cyan-400/30">
                  <Activity className="w-3 h-3 ml-1" />
                  لوحة التحكم
                </Badge>
              </motion.div>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <Notifications />
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 }}
                className="flex items-center space-x-2 space-x-reverse"
              >
                <div className="text-2xl">
                  <User className="w-8 h-8 text-cyan-400" />
                </div>
                <div className="text-right">
                  <div className="text-white font-medium">مرحباً، {user.name}</div>
                  <div className="text-slate-400 text-sm">{user.email}</div>
                </div>
              </motion.div>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-slate-600 text-slate-300 hover:border-cyan-400 hover:text-cyan-400"
                >
                  <Settings className="w-4 h-4 ml-2" />
                  الإعدادات
                </Button>
              </motion.div>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleLogout}
                  className="border-red-600 text-red-400 hover:bg-red-600 hover:text-white"
                >
                  <LogOut className="w-4 h-4 ml-2" />
                  تسجيل الخروج
                </Button>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.header>

      <div className="container mx-auto px-4 py-8 relative z-10">
        {/* Stats Cards */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
        >
          {[
            {
              title: "الرصيد الحالي",
              value: user.balance,
              prefix: "$",
              icon: DollarSign,
              color: "from-green-400 to-emerald-600",
              bgColor: "bg-green-400/10",
            },
            {
              title: "إجمالي الطلبات",
              value: orders.length,
              icon: ShoppingCart,
              color: "from-blue-400 to-cyan-600",
              bgColor: "bg-blue-400/10",
            },
            {
              title: "طلبات مكتملة",
              value: orders.filter((order) => order.status === "مكتمل").length,
              icon: CheckCircle,
              color: "from-green-400 to-teal-600",
              bgColor: "bg-green-400/10",
            },
            {
              title: "طلبات معلقة",
              value: orders.filter((order) => order.status === "معلق" || order.status === "قيد التنفيذ").length,
              icon: Clock,
              color: "from-yellow-400 to-orange-600",
              bgColor: "bg-yellow-400/10",
            },
          ].map((stat, index) => (
            <motion.div key={index} variants={itemVariants}>
              <motion.div
                whileHover={{
                  scale: 1.05,
                  y: -5,
                }}
                className="group"
              >
                <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm hover:border-cyan-400/50 transition-all duration-500 relative overflow-hidden">
                  <motion.div
                    className={`absolute inset-0 ${stat.bgColor} opacity-0 group-hover:opacity-100 transition-opacity duration-500`}
                  />
                  <CardContent className="p-6 relative z-10">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-300 text-sm mb-1">{stat.title}</p>
                        <motion.p
                          className="text-2xl font-bold text-white"
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ delay: index * 0.1 + 0.5, type: "spring" }}
                        >
                          <AnimatedCounter end={stat.value} prefix={stat.prefix} />
                        </motion.p>
                      </div>
                      <motion.div
                        animate={{
                          rotate: [0, 5, -5, 0],
                        }}
                        transition={{
                          duration: 4,
                          repeat: Number.POSITIVE_INFINITY,
                          ease: "easeInOut",
                        }}
                      >
                        <stat.icon className={`w-8 h-8 bg-gradient-to-r ${stat.color} bg-clip-text text-transparent`} />
                      </motion.div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </motion.div>
          ))}
        </motion.div>

        {/* Social Links Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mb-8"
        >
          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-cyan-400" />
                تابعنا على وسائل التواصل الاجتماعي
              </CardTitle>
              <CardDescription className="text-slate-300">ابق على اطلاع بآخر العروض والخدمات الجديدة</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-center">
                <SocialLinks />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Main Content */}
        <motion.div initial={{ opacity: 0, y: 50 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.8 }}>
          <Tabs defaultValue="orders" className="space-y-6">
            <TabsList className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
              {["orders", "new-order", "balance"].map((tab, index) => (
                <motion.div
                  key={tab}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 + 1 }}
                >
                  <TabsTrigger
                    value={tab}
                    className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-cyan-400 data-[state=active]:to-blue-500 data-[state=active]:text-slate-900 transition-all duration-300"
                  >
                    {tab === "orders" && "طلباتي"}
                    {tab === "new-order" && "طلب جديد"}
                    {tab === "balance" && "الرصيد"}
                  </TabsTrigger>
                </motion.div>
              ))}
            </TabsList>

            <AnimatePresence mode="wait">
              <TabsContent value="orders">
                <motion.div
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 50 }}
                  transition={{ duration: 0.5 }}
                >
                  <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                    <CardHeader>
                      <CardTitle className="text-white flex items-center gap-2">
                        <TrendingUp className="w-5 h-5 text-cyan-400" />
                        طلباتي
                      </CardTitle>
                      <CardDescription className="text-slate-300">تتبع جميع طلباتك وحالتها</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {orders.length === 0 ? (
                          <div className="text-center py-12">
                            <div className="text-6xl mb-4">📦</div>
                            <h3 className="text-2xl font-bold text-white mb-2">لا توجد طلبات بعد</h3>
                            <p className="text-slate-300">ابدأ بطلب خدمة جديدة من تبويب "طلب جديد"</p>
                          </div>
                        ) : (
                          orders.map((order, index) => (
                            <motion.div
                              key={order.id}
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: index * 0.1 }}
                              whileHover={{ scale: 1.02, x: 5 }}
                              className="group"
                            >
                              <div className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg hover:bg-slate-700/50 transition-all duration-300 relative overflow-hidden">
                                <motion.div className="absolute inset-0 bg-gradient-to-r from-cyan-400/5 to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                                <div className="flex-1 relative z-10">
                                  <div className="flex items-center space-x-4 space-x-reverse">
                                    <div>
                                      <p className="text-white font-medium group-hover:text-cyan-400 transition-colors duration-300">
                                        {order.serviceName}
                                      </p>
                                      <p className="text-slate-300 text-sm">
                                        الكمية: {order.quantity.toLocaleString()}
                                      </p>
                                      <p className="text-slate-300 text-sm">الرابط: {order.link}</p>
                                      <div className="mt-2">
                                        <div className="flex items-center gap-2 mb-1">
                                          <span className="text-xs text-slate-400">التقدم:</span>
                                          <span className="text-xs text-cyan-400">{order.progress}%</span>
                                        </div>
                                        <Progress value={order.progress} className="h-1" />
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-4 space-x-reverse relative z-10">
                                  <motion.div whileHover={{ scale: 1.1 }} className="text-center">
                                    <Badge
                                      className={`${getStatusColor(order.status)} hover:shadow-lg transition-all duration-300`}
                                    >
                                      {order.status}
                                    </Badge>
                                  </motion.div>
                                  <div className="text-left">
                                    <p className="text-white font-medium">${order.price}</p>
                                    <p className="text-slate-300 text-sm">
                                      {format(new Date(order.createdAt), "dd/MM/yyyy", { locale: arSA })}
                                    </p>
                                  </div>
                                  <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      className="border-slate-600 hover:border-cyan-400 hover:text-cyan-400"
                                    >
                                      <Eye className="w-4 h-4" />
                                    </Button>
                                  </motion.div>
                                </div>
                              </div>
                            </motion.div>
                          ))
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </TabsContent>

              <TabsContent value="new-order">
                <motion.div
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 50 }}
                  transition={{ duration: 0.5 }}
                >
                  <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                    <CardHeader>
                      <CardTitle className="text-white flex items-center gap-2">
                        <Plus className="w-5 h-5 text-cyan-400" />
                        طلب خدمة جديدة
                      </CardTitle>
                      <CardDescription className="text-slate-300">اختر الخدمة التي تريدها وابدأ طلبك</CardDescription>
                    </CardHeader>
                    <CardContent>
                      {/* Search and Filter */}
                      <div className="flex flex-col md:flex-row gap-4 mb-8">
                        <div className="flex-1 relative">
                          <Search className="absolute right-3 top-3 h-4 w-4 text-slate-400" />
                          <Input
                            placeholder="البحث عن خدمة..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pr-10 bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400"
                          />
                        </div>
                        <div className="flex gap-2 overflow-x-auto">
                          <Button
                            variant={selectedCategory === "all" ? "default" : "outline"}
                            onClick={() => setSelectedCategory("all")}
                            className={`whitespace-nowrap ${
                              selectedCategory === "all"
                                ? "bg-cyan-400 text-slate-900"
                                : "border-slate-600 text-slate-300 hover:bg-slate-700"
                            }`}
                          >
                            <Filter className="w-4 h-4 ml-2" />
                            جميع الخدمات
                          </Button>
                          {categories.map((category) => (
                            <Button
                              key={category.id}
                              variant={selectedCategory === category.id ? "default" : "outline"}
                              onClick={() => setSelectedCategory(category.id)}
                              className={`whitespace-nowrap ${
                                selectedCategory === category.id
                                  ? "bg-cyan-400 text-slate-900"
                                  : "border-slate-600 text-slate-300 hover:bg-slate-700"
                              }`}
                            >
                              <span className="ml-2">{category.icon}</span>
                              {category.name}
                            </Button>
                          ))}
                        </div>
                      </div>

                      {/* Services Grid */}
                      <motion.div
                        variants={containerVariants}
                        initial="hidden"
                        animate="visible"
                        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                      >
                        {filteredServices.map((service, index) => (
                          <motion.div key={service.id} variants={itemVariants}>
                            <ServiceCard service={service} onOrderClick={handleServiceOrder} />
                          </motion.div>
                        ))}
                      </motion.div>

                      {/* No Results */}
                      {filteredServices.length === 0 && (
                        <div className="text-center py-12">
                          <div className="text-6xl mb-4">🔍</div>
                          <h3 className="text-2xl font-bold text-white mb-2">لم نجد أي خدمات</h3>
                          <p className="text-slate-300">جرب البحث بكلمات مختلفة أو اختر فئة أخرى</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              </TabsContent>

              <TabsContent value="balance">
                <motion.div
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 50 }}
                  transition={{ duration: 0.5 }}
                  className="grid grid-cols-1 md:grid-cols-2 gap-6"
                >
                  <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                    <CardHeader>
                      <CardTitle className="text-white flex items-center gap-2">
                        <DollarSign className="w-5 h-5 text-green-400" />
                        شحن الرصيد
                      </CardTitle>
                      <CardDescription className="text-slate-300">أضف أموال إلى حسابك</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <motion.div
                        variants={containerVariants}
                        initial="hidden"
                        animate="visible"
                        className="grid grid-cols-2 gap-4"
                      >
                        {[10, 25, 50, 100].map((amount, index) => (
                          <motion.div key={amount} variants={itemVariants}>
                            <motion.div whileHover={{ scale: 1.05, y: -2 }} whileTap={{ scale: 0.95 }}>
                              <Button
                                variant="outline"
                                onClick={() => handleChargeBalance(amount)}
                                className="w-full border-slate-600 text-white hover:bg-gradient-to-r hover:from-cyan-400 hover:to-blue-500 hover:text-slate-900 hover:border-transparent transition-all duration-300 hover:shadow-[0_0_20px_rgba(6,182,212,0.3)]"
                              >
                                <motion.span
                                  animate={{
                                    textShadow: [
                                      "0 0 0px rgba(6,182,212,0)",
                                      "0 0 10px rgba(6,182,212,0.5)",
                                      "0 0 0px rgba(6,182,212,0)",
                                    ],
                                  }}
                                  transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
                                >
                                  ${amount}
                                </motion.span>
                              </Button>
                            </motion.div>
                          </motion.div>
                        ))}
                      </motion.div>
                      <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                        <Button
                          onClick={() => handleChargeBalance(0)}
                          className="w-full bg-gradient-to-r from-cyan-400 to-blue-500 text-slate-900 hover:from-cyan-500 hover:to-blue-600 transition-all duration-300 hover:shadow-[0_0_30px_rgba(6,182,212,0.6)]"
                        >
                          <Zap className="w-4 h-4 ml-2" />
                          مبلغ مخصص
                        </Button>
                      </motion.div>
                    </CardContent>
                  </Card>

                  <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                    <CardHeader>
                      <CardTitle className="text-white flex items-center gap-2">
                        <Activity className="w-5 h-5 text-purple-400" />
                        تاريخ المعاملات
                      </CardTitle>
                      <CardDescription className="text-slate-300">آخر العمليات المالية</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {transactions.length === 0 ? (
                          <div className="text-center py-6">
                            <p className="text-slate-400">لا توجد معاملات بعد</p>
                          </div>
                        ) : (
                          transactions
                            .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
                            .slice(0, 5)
                            .map((transaction, index) => (
                              <motion.div
                                key={transaction.id}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: index * 0.1 }}
                                whileHover={{ scale: 1.02, x: 5 }}
                                className="flex justify-between items-center p-3 bg-slate-700/30 rounded hover:bg-slate-700/50 transition-all duration-300 group"
                              >
                                <div className="flex items-center gap-3">
                                  <span className="text-xl">
                                    {transaction.type === "شحن" ? "💰" : transaction.type === "طلب" ? "🛒" : "💸"}
                                  </span>
                                  <div>
                                    <p className="text-white group-hover:text-cyan-400 transition-colors duration-300">
                                      {transaction.type}
                                    </p>
                                    <p className="text-slate-300 text-sm">
                                      {format(new Date(transaction.createdAt), "dd/MM/yyyy", { locale: arSA })}
                                    </p>
                                  </div>
                                </div>
                                <div className="text-left">
                                  <motion.p
                                    className={`font-bold ${
                                      transaction.amount > 0 ? "text-green-400" : "text-red-400"
                                    }`}
                                    whileHover={{ scale: 1.1 }}
                                  >
                                    {transaction.amount > 0 ? "+" : ""}${Math.abs(transaction.amount)}
                                  </motion.p>
                                  <Badge
                                    className={`text-xs ${
                                      transaction.status === "مكتمل"
                                        ? "bg-green-500"
                                        : transaction.status === "معلق"
                                          ? "bg-yellow-500"
                                          : "bg-red-500"
                                    }`}
                                  >
                                    {transaction.status}
                                  </Badge>
                                </div>
                              </motion.div>
                            ))
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </TabsContent>
            </AnimatePresence>
          </Tabs>
        </motion.div>
      </div>
    </div>
  )
}
