"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Copy, Check, Banknote } from "lucide-react"
import { paymentMethods, exchangeRate } from "@/lib/data"

interface PaymentMethodsProps {
  amount: number
  onPaymentComplete: (method: string, transactionId: string) => void
}

const PaymentMethods = ({ amount, onPaymentComplete }: PaymentMethodsProps) => {
  const [selectedMethod, setSelectedMethod] = useState<string | null>(null)
  const [transactionId, setTransactionId] = useState("")
  const [copied, setCopied] = useState<string | null>(null)
  const [customAmount, setCustomAmount] = useState<number | "">("")
  const finalAmount = customAmount !== "" ? customAmount : amount

  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text)
    setCopied(type)
    setTimeout(() => setCopied(null), 2000)
  }

  const handlePaymentSubmit = () => {
    if (selectedMethod && transactionId.trim()) {
      onPaymentComplete(selectedMethod, transactionId)
    }
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-2xl font-bold text-white mb-2">اختر طريقة الدفع</h3>
        <p className="text-slate-300">
          المبلغ المطلوب: <span className="text-cyan-400 font-bold">${finalAmount}</span>{" "}
          <span className="text-slate-400">({Math.round(finalAmount * exchangeRate)} جنيه مصري)</span>
        </p>
      </div>

      {/* Custom Amount Input */}
      <div className="space-y-2">
        <Label htmlFor="custom-amount" className="text-white">
          المبلغ المطلوب (بالدولار)
        </Label>
        <div className="relative">
          <Input
            id="custom-amount"
            type="number"
            min="1"
            placeholder="أدخل المبلغ المطلوب"
            value={customAmount}
            onChange={(e) => setCustomAmount(e.target.value ? Number(e.target.value) : "")}
            className="bg-slate-700/50 border-slate-600 text-white pr-16"
          />
          <div className="absolute left-3 top-1/2 -translate-y-1/2 text-slate-400">
            ${customAmount !== "" ? customAmount : ""}
          </div>
        </div>
        <p className="text-xs text-slate-400">
          المبلغ بالجنيه المصري: {customAmount !== "" ? Math.round(Number(customAmount) * exchangeRate) : 0} جنيه
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {paymentMethods.map((method) => (
          <motion.div
            key={method.id}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setSelectedMethod(method.id)}
          >
            <Card
              className={`cursor-pointer transition-all duration-300 ${
                selectedMethod === method.id
                  ? "border-cyan-400 bg-slate-700/50"
                  : "border-slate-700 bg-slate-800/50 hover:border-slate-600"
              }`}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg bg-gradient-to-r ${method.color}`}>
                    <method.icon className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-white text-lg">{method.name}</CardTitle>
                    <CardDescription className="text-slate-300">{method.description}</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                  <span className="text-white font-mono text-lg">{method.number}</span>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={(e) => {
                      e.stopPropagation()
                      copyToClipboard(method.number, method.id)
                    }}
                    className="p-2 rounded-lg bg-cyan-400/20 text-cyan-400 hover:bg-cyan-400/30 transition-colors"
                  >
                    {copied === method.id ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                  </motion.button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      <AnimatePresence>
        {selectedMethod && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-4"
          >
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Banknote className="w-5 h-5 text-green-400" />
                  تأكيد الدفع
                </CardTitle>
                <CardDescription className="text-slate-300">أدخل رقم العملية بعد إتمام التحويل</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="transaction" className="text-white">
                    رقم العملية / Transaction ID
                  </Label>
                  <Input
                    id="transaction"
                    placeholder="أدخل رقم العملية هنا"
                    value={transactionId}
                    onChange={(e) => setTransactionId(e.target.value)}
                    className="bg-slate-700/50 border-slate-600 text-white"
                  />
                </div>

                <div className="p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
                  <p className="text-yellow-400 text-sm">⚠️ تأكد من إرسال المبلغ الصحيح وحفظ رقم العملية قبل المتابعة</p>
                </div>

                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                  <Button
                    onClick={handlePaymentSubmit}
                    disabled={!transactionId.trim()}
                    className="w-full bg-gradient-to-r from-green-500 to-green-700 text-white hover:from-green-600 hover:to-green-800 transition-all duration-300 hover:shadow-[0_0_20px_rgba(34,197,94,0.5)]"
                  >
                    تأكيد الدفع
                  </Button>
                </motion.div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default PaymentMethods
