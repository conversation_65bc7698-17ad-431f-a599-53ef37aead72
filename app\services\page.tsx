"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, Star, Clock, Shield, TrendingUp, Filter } from "lucide-react"
import Link from "next/link"
import { motion, AnimatePresence } from "framer-motion"
import { useAuth } from "@/lib/auth"
import { services, categories } from "@/lib/data"
import ServiceCard from "@/components/service-card"
import OrderModal from "@/components/order-modal"
import type { Service } from "@/lib/types"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CheckCircle, AlertCircle } from "lucide-react"

export default function ServicesPage() {
  const { user, addOrder, addNotification } = useAuth()
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedService, setSelectedService] = useState<Service | null>(null)
  const [showOrderModal, setShowOrderModal] = useState(false)
  const [notification, setNotification] = useState<{ type: "success" | "error"; message: string } | null>(null)

  const filteredServices = services.filter((service) => {
    const matchesSearch = service.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "all" || service.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const handleServiceOrder = (service: Service) => {
    if (!user) {
      setNotification({
        type: "error",
        message: "يجب تسجيل الدخول أولاً لطلب الخدمات",
      })
      setTimeout(() => setNotification(null), 5000)
      return
    }

    setSelectedService(service)
    setShowOrderModal(true)
  }

  const handleOrderSubmit = async (orderData: {
    serviceId: number
    serviceName: string
    quantity: number
    price: number
    link: string
  }) => {
    if (!user) return

    if (user.balance < orderData.price) {
      setNotification({
        type: "error",
        message: "رصيدك غير كافي لإتمام هذا الطلب. يرجى شحن رصيدك أولاً.",
      })
      setTimeout(() => setNotification(null), 5000)
      return
    }

    const success = await addOrder(orderData)
    if (success) {
      setNotification({
        type: "success",
        message: "تم إنشاء الطلب بنجاح! سيتم تنفيذه قريبًا.",
      })
      setShowOrderModal(false)
      setSelectedService(null)
    } else {
      setNotification({
        type: "error",
        message: "حدث خطأ أثناء إنشاء الطلب. يرجى المحاولة مرة أخرى.",
      })
    }
    setTimeout(() => setNotification(null), 5000)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900" dir="rtl">
      {/* Notification */}
      <AnimatePresence>
        {notification && (
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            className="fixed top-4 right-4 z-50"
          >
            <Alert
              className={`${notification.type === "success" ? "border-green-500/50 bg-green-500/10" : "border-red-500/50 bg-red-500/10"}`}
            >
              {notification.type === "success" ? (
                <CheckCircle className="h-4 w-4 text-green-400" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-400" />
              )}
              <AlertDescription className={notification.type === "success" ? "text-green-400" : "text-red-400"}>
                {notification.message}
              </AlertDescription>
            </Alert>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Order Modal */}
      <AnimatePresence>
        {showOrderModal && (
          <OrderModal
            service={selectedService}
            onClose={() => {
              setShowOrderModal(false)
              setSelectedService(null)
            }}
            onSubmit={handleOrderSubmit}
          />
        )}
      </AnimatePresence>

      {/* Header */}
      <header className="border-b border-slate-700/50 backdrop-blur-sm bg-slate-900/80 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="text-2xl font-bold text-cyan-400">
              smmElsoury
            </Link>
            <nav className="hidden md:flex items-center space-x-8 space-x-reverse">
              <Link href="/" className="text-white hover:text-cyan-400 transition-colors">
                الرئيسية
              </Link>
              <Link href="/services" className="text-cyan-400">
                الخدمات
              </Link>
              <Link href="/support" className="text-white hover:text-cyan-400 transition-colors">
                الدعم الفني
              </Link>
              <Link href="/terms" className="text-white hover:text-cyan-400 transition-colors">
                الشروط والأحكام
              </Link>
            </nav>
            <div className="flex items-center space-x-4 space-x-reverse">
              {user ? (
                <Link href="/dashboard">
                  <Button className="bg-cyan-400 text-slate-900 hover:bg-cyan-500">لوحة التحكم</Button>
                </Link>
              ) : (
                <>
                  <Link href="/auth/login">
                    <Button
                      variant="outline"
                      className="border-cyan-400 text-cyan-400 hover:bg-cyan-400 hover:text-slate-900"
                    >
                      تسجيل الدخول
                    </Button>
                  </Link>
                  <Link href="/auth/register">
                    <Button className="bg-cyan-400 text-slate-900 hover:bg-cyan-500">إنشاء حساب</Button>
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">خدماتنا المميزة</h1>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto">
            اكتشف مجموعة واسعة من خدمات التسويق الرقمي بأفضل الأسعار وأعلى جودة
          </p>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="flex-1 relative">
            <Search className="absolute right-3 top-3 h-4 w-4 text-slate-400" />
            <Input
              placeholder="البحث عن خدمة..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pr-10 bg-slate-800/50 border-slate-700 text-white placeholder:text-slate-400"
            />
          </div>
          <div className="flex gap-2 overflow-x-auto">
            <Button
              variant={selectedCategory === "all" ? "default" : "outline"}
              onClick={() => setSelectedCategory("all")}
              className={`whitespace-nowrap ${
                selectedCategory === "all"
                  ? "bg-cyan-400 text-slate-900"
                  : "border-slate-600 text-slate-300 hover:bg-slate-700"
              }`}
            >
              <Filter className="w-4 h-4 ml-2" />
              جميع الخدمات
            </Button>
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                onClick={() => setSelectedCategory(category.id)}
                className={`whitespace-nowrap ${
                  selectedCategory === category.id
                    ? "bg-cyan-400 text-slate-900"
                    : "border-slate-600 text-slate-300 hover:bg-slate-700"
                }`}
              >
                <span className="ml-2">{category.icon}</span>
                {category.name}
              </Button>
            ))}
          </div>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredServices.map((service) => (
            <ServiceCard key={service.id} service={service} onOrderClick={handleServiceOrder} />
          ))}
        </div>

        {/* No Results */}
        {filteredServices.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-2xl font-bold text-white mb-2">لم نجد أي خدمات</h3>
            <p className="text-slate-300">جرب البحث بكلمات مختلفة أو اختر فئة أخرى</p>
          </div>
        )}

        {/* Why Choose Us Section */}
        <section className="mt-20 py-16 bg-slate-800/30 rounded-2xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">لماذا تختار خدماتنا؟</h2>
            <p className="text-xl text-slate-300">نحن نقدم أفضل الخدمات بأعلى جودة</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: <Star className="w-8 h-8 text-yellow-400" />,
                title: "جودة عالية",
                description: "جميع خدماتنا من مصادر حقيقية ونشطة",
              },
              {
                icon: <Clock className="w-8 h-8 text-green-400" />,
                title: "تنفيذ سريع",
                description: "نبدأ تنفيذ طلبك خلال دقائق من الطلب",
              },
              {
                icon: <Shield className="w-8 h-8 text-blue-400" />,
                title: "أمان مضمون",
                description: "حماية كاملة لحسابك وبياناتك",
              },
              {
                icon: <TrendingUp className="w-8 h-8 text-purple-400" />,
                title: "نتائج مضمونة",
                description: "ضمان الحصول على النتائج المطلوبة",
              },
            ].map((feature, index) => (
              <div key={index} className="text-center">
                <div className="mb-4">{feature.icon}</div>
                <h3 className="text-xl font-bold text-white mb-2">{feature.title}</h3>
                <p className="text-slate-300">{feature.description}</p>
              </div>
            ))}
          </div>
        </section>
      </div>
    </div>
  )
}
