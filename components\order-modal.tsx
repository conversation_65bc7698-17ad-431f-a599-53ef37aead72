"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AlertCircle, X } from "lucide-react"
import type { Service } from "@/lib/types"
import { exchangeRate } from "@/lib/data"

interface OrderModalProps {
  service: Service | null
  onClose: () => void
  onSubmit: (orderData: {
    serviceId: number
    serviceName: string
    quantity: number
    price: number
    link: string
  }) => void
}

export default function OrderModal({ service, onClose, onSubmit }: OrderModalProps) {
  const [quantity, setQuantity] = useState<number>(0)
  const [link, setLink] = useState("")
  const [error, setError] = useState<string | null>(null)
  const [totalPrice, setTotalPrice] = useState(0)

  useEffect(() => {
    if (service) {
      setQuantity(service.minOrder)
    }
  }, [service])

  useEffect(() => {
    if (service) {
      const price = (quantity / 1000) * service.price
      setTotalPrice(price)
    }
  }, [quantity, service])

  const handleSubmit = () => {
    if (!service) return

    setError(null)

    if (quantity < service.minOrder) {
      setError(`الحد الأدنى للطلب هو ${service.minOrder}`)
      return
    }

    if (quantity > service.maxOrder) {
      setError(`الحد الأقصى للطلب هو ${service.maxOrder}`)
      return
    }

    if (!link) {
      setError("يرجى إدخال رابط صحيح")
      return
    }

    onSubmit({
      serviceId: service.id,
      serviceName: service.name,
      quantity,
      price: totalPrice,
      link,
    })
  }

  if (!service) return null

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        onClick={(e) => e.stopPropagation()}
        className="w-full max-w-md"
      >
        <Card className="bg-slate-800/90 border-slate-700 backdrop-blur-sm">
          <CardHeader className="relative">
            <Button
              variant="ghost"
              size="icon"
              className="absolute left-2 top-2 text-slate-400 hover:text-white"
              onClick={onClose}
            >
              <X className="h-4 w-4" />
            </Button>
            <CardTitle className="text-white flex items-center gap-2">
              <span className="text-2xl">
                {service.category === "instagram"
                  ? "📸"
                  : service.category === "facebook"
                    ? "📘"
                    : service.category === "youtube"
                      ? "📺"
                      : service.category === "tiktok"
                        ? "🎵"
                        : service.category === "twitter"
                          ? "🐦"
                          : "👻"}
              </span>
              طلب خدمة
            </CardTitle>
            <CardDescription className="text-slate-300">{service.name}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {error && (
              <div className="p-3 bg-red-500/10 border border-red-500/30 rounded-lg flex items-center gap-2 text-red-400">
                <AlertCircle className="h-4 w-4" />
                <p className="text-sm">{error}</p>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="link" className="text-white">
                رابط الصفحة / المنشور
              </Label>
              <Input
                id="link"
                placeholder="أدخل الرابط هنا"
                value={link}
                onChange={(e) => setLink(e.target.value)}
                className="bg-slate-700/50 border-slate-600 text-white"
              />
              <p className="text-xs text-slate-400">مثال: https://www.instagram.com/username</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="quantity" className="text-white">
                الكمية
              </Label>
              <Input
                id="quantity"
                type="number"
                min={service.minOrder}
                max={service.maxOrder}
                value={quantity}
                onChange={(e) => setQuantity(Number(e.target.value))}
                className="bg-slate-700/50 border-slate-600 text-white"
              />
              <p className="text-xs text-slate-400">
                الحد الأدنى: {service.minOrder} | الحد الأقصى: {service.maxOrder}
              </p>
            </div>

            <div className="p-4 bg-slate-700/30 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-slate-300">السعر:</span>
                <span className="text-cyan-400 font-bold">${totalPrice.toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center mt-1">
                <span className="text-slate-400 text-sm">بالجنيه المصري:</span>
                <span className="text-slate-300 text-sm">{Math.round(totalPrice * exchangeRate)} جنيه</span>
              </div>
            </div>

            <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
              <Button
                onClick={handleSubmit}
                className="w-full bg-gradient-to-r from-cyan-400 to-blue-500 text-slate-900 hover:from-cyan-500 hover:to-blue-600 transition-all duration-300 hover:shadow-[0_0_20px_rgba(6,182,212,0.5)]"
              >
                تأكيد الطلب
              </Button>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  )
}
