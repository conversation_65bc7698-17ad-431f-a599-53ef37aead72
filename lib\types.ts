export interface User {
  id: string
  name: string
  email: string
  role: "user" | "admin"
  avatar?: string
  balance: number
  isVerified: boolean
  createdAt: string
}

export interface Service {
  id: number
  name: string
  category: string
  description: string
  price: number
  minOrder: number
  maxOrder: number
  features: string[]
  status: "نشط" | "متوقف"
  rating?: number
  orders?: number
}

export interface Order {
  id: string
  userId: string
  serviceId: number
  serviceName: string
  quantity: number
  price: number
  link: string
  status: "معلق" | "قيد التنفيذ" | "مكتمل" | "ملغي"
  progress: number
  createdAt: string
}

export interface Transaction {
  id: string
  userId: string
  type: "شحن" | "طلب" | "استرداد"
  amount: number
  description: string
  status: "معلق" | "مكتمل" | "مرفوض"
  paymentMethod?: string
  paymentId?: string
  createdAt: string
}

export interface PaymentMethod {
  id: string
  name: string
  number: string
  icon: any
  color: string
  description: string
}

export interface Category {
  id: string
  name: string
  slug: string
  icon: string
}

export interface Notification {
  id: string
  userId: string
  title: string
  message: string
  type: "success" | "error" | "warning" | "info"
  read: boolean
  createdAt: string
}
