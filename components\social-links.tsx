"use client"

import { motion } from "framer-motion"
import { Facebook, Instagram, Youtube } from "lucide-react"
import Link from "next/link"

const SocialLinks = ({ className = "" }: { className?: string }) => {
  const socialLinks = [
    {
      name: "TikTok",
      url: "https://www.tiktok.com/@elsoury_marketing?_t=ZS-8wzhmhtDJhg&_r=1",
      icon: "🎵",
      color: "from-pink-500 to-red-500",
      hoverColor: "hover:shadow-[0_0_20px_rgba(236,72,153,0.5)]",
    },
    {
      name: "Facebook",
      url: "https://www.facebook.com/share/1AfseAB525/?mibextid=wwXIfr",
      icon: Facebook,
      color: "from-blue-600 to-blue-800",
      hoverColor: "hover:shadow-[0_0_20px_rgba(37,99,235,0.5)]",
    },
    {
      name: "Instagram",
      url: "https://www.instagram.com/xx_bedoo_elsoury_xx?igsh=OHU1czZmZG13aHZm&utm_source=qr",
      icon: Instagram,
      color: "from-purple-500 to-pink-500",
      hoverColor: "hover:shadow-[0_0_20px_rgba(168,85,247,0.5)]",
    },
    {
      name: "YouTube",
      url: "https://youtube.com/@elsoury_marketing?si=ZPq6cib5kE6leMC7",
      icon: Youtube,
      color: "from-red-500 to-red-700",
      hoverColor: "hover:shadow-[0_0_20px_rgba(239,68,68,0.5)]",
    },
  ]

  return (
    <div className={`flex items-center gap-4 ${className}`}>
      {socialLinks.map((social, index) => (
        <motion.div
          key={social.name}
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: index * 0.1, type: "spring", stiffness: 200 }}
          whileHover={{ scale: 1.1, y: -2 }}
          whileTap={{ scale: 0.95 }}
        >
          <Link
            href={social.url}
            target="_blank"
            rel="noopener noreferrer"
            className={`flex items-center justify-center w-10 h-10 rounded-full bg-gradient-to-r ${social.color} text-white transition-all duration-300 ${social.hoverColor}`}
          >
            {typeof social.icon === "string" ? (
              <span className="text-lg">{social.icon}</span>
            ) : (
              <social.icon className="w-5 h-5" />
            )}
          </Link>
        </motion.div>
      ))}
    </div>
  )
}

export default SocialLinks
