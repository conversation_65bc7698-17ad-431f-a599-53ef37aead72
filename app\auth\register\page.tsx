"use client"

import type React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription } from "@/components/ui/alert"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { Eye, EyeOff, Mail, Lock, User, Phone, Sparkles, UserPlus, AlertCircle, CheckCircle } from "lucide-react"
import { motion } from "framer-motion"
import { useAuth } from "@/lib/auth"
import SocialLinks from "@/components/social-links"

export default function RegisterPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    password: "",
    confirmPassword: "",
    agreeToTerms: false,
  })
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const router = useRouter()
  const { register, loginWithGoogle, loginWithFacebook, isLoading } = useAuth()

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setSuccess("")

    if (formData.password !== formData.confirmPassword) {
      setError("كلمات المرور غير متطابقة")
      return
    }

    if (formData.password.length < 6) {
      setError("كلمة المرور يجب أن تكون 6 أحرف على الأقل")
      return
    }

    if (!formData.agreeToTerms) {
      setError("يجب الموافقة على الشروط والأحكام")
      return
    }

    const success = await register(formData)
    if (success) {
      setSuccess("تم إنشاء الحساب بنجاح! سيتم توجيهك إلى لوحة التحكم...")
      setTimeout(() => {
        router.push("/dashboard")
      }, 2000)
    } else {
      setError("فشل في إنشاء الحساب. حاول مرة أخرى.")
    }
  }

  const updateFormData = (field: string, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleSocialLogin = async (provider: "google" | "facebook") => {
    setError("")
    const success = provider === "google" ? await loginWithGoogle() : await loginWithFacebook()
    if (success) {
      router.push("/dashboard")
    } else {
      setError(`فشل في التسجيل عبر ${provider === "google" ? "Google" : "Facebook"}`)
    }
  }

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center p-4 relative overflow-hidden"
      dir="rtl"
    >
      {/* Animated Background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <motion.div
          className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-green-400/20 to-emerald-600/20 rounded-full blur-3xl"
          animate={{
            x: [0, 100, 0],
            y: [0, -100, 0],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 20,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-purple-400/20 to-pink-600/20 rounded-full blur-3xl"
          animate={{
            x: [0, -100, 0],
            y: [0, 100, 0],
            scale: [1, 1.3, 1],
          }}
          transition={{
            duration: 25,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
        />
      </div>

      <motion.div
        initial={{ opacity: 0, y: 50, scale: 0.9 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="relative z-10 w-full max-w-md"
      >
        <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm relative overflow-hidden">
          {/* Animated border */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-green-400/50 to-emerald-500/50 rounded-lg"
            animate={{
              opacity: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 3,
              repeat: Number.POSITIVE_INFINITY,
              ease: "easeInOut",
            }}
          />
          <div className="absolute inset-[1px] bg-slate-800/90 rounded-lg backdrop-blur-sm" />

          <CardHeader className="text-center relative z-10">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
              className="text-3xl font-bold mb-2"
            >
              <motion.span
                className="bg-gradient-to-r from-green-400 to-emerald-500 bg-clip-text text-transparent"
                animate={{
                  textShadow: [
                    "0 0 10px rgba(34,197,94,0.5)",
                    "0 0 20px rgba(34,197,94,0.8)",
                    "0 0 10px rgba(34,197,94,0.5)",
                  ],
                }}
                transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
              >
                smmElsoury
              </motion.span>
            </motion.div>
            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.5 }}>
              <CardTitle className="text-2xl text-white flex items-center justify-center gap-2">
                <UserPlus className="w-6 h-6 text-green-400" />
                إنشاء حساب جديد
              </CardTitle>
              <CardDescription className="text-slate-300 mt-2">انضم إلينا واحصل على أفضل خدمات SMM</CardDescription>
            </motion.div>

            {/* Social Links */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
              className="flex justify-center mt-4"
            >
              <SocialLinks />
            </motion.div>
          </CardHeader>

          <CardContent className="relative z-10 space-y-4">
            {error && (
              <motion.div initial={{ opacity: 0, y: -10 }} animate={{ opacity: 1, y: 0 }}>
                <Alert className="border-red-500/50 bg-red-500/10">
                  <AlertCircle className="h-4 w-4 text-red-400" />
                  <AlertDescription className="text-red-400">{error}</AlertDescription>
                </Alert>
              </motion.div>
            )}

            {success && (
              <motion.div initial={{ opacity: 0, y: -10 }} animate={{ opacity: 1, y: 0 }}>
                <Alert className="border-green-500/50 bg-green-500/10">
                  <CheckCircle className="h-4 w-4 text-green-400" />
                  <AlertDescription className="text-green-400">{success}</AlertDescription>
                </Alert>
              </motion.div>
            )}

            <form onSubmit={handleRegister} className="space-y-4">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6 }}
                className="space-y-2"
              >
                <Label htmlFor="name" className="text-white">
                  الاسم الكامل
                </Label>
                <div className="relative group">
                  <User className="absolute right-3 top-3 h-4 w-4 text-slate-400 group-focus-within:text-green-400 transition-colors duration-300" />
                  <Input
                    id="name"
                    type="text"
                    placeholder="أدخل اسمك الكامل"
                    value={formData.name}
                    onChange={(e) => updateFormData("name", e.target.value)}
                    className="pr-10 bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 focus:border-green-400 focus:ring-green-400/20 transition-all duration-300"
                    required
                  />
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.7 }}
                className="space-y-2"
              >
                <Label htmlFor="email" className="text-white">
                  البريد الإلكتروني
                </Label>
                <div className="relative group">
                  <Mail className="absolute right-3 top-3 h-4 w-4 text-slate-400 group-focus-within:text-green-400 transition-colors duration-300" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="أدخل بريدك الإلكتروني"
                    value={formData.email}
                    onChange={(e) => updateFormData("email", e.target.value)}
                    className="pr-10 bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 focus:border-green-400 focus:ring-green-400/20 transition-all duration-300"
                    required
                  />
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.8 }}
                className="space-y-2"
              >
                <Label htmlFor="phone" className="text-white">
                  رقم الهاتف
                </Label>
                <div className="relative group">
                  <Phone className="absolute right-3 top-3 h-4 w-4 text-slate-400 group-focus-within:text-green-400 transition-colors duration-300" />
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="أدخل رقم هاتفك"
                    value={formData.phone}
                    onChange={(e) => updateFormData("phone", e.target.value)}
                    className="pr-10 bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 focus:border-green-400 focus:ring-green-400/20 transition-all duration-300"
                    required
                  />
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.9 }}
                className="space-y-2"
              >
                <Label htmlFor="password" className="text-white">
                  كلمة المرور
                </Label>
                <div className="relative group">
                  <Lock className="absolute right-3 top-3 h-4 w-4 text-slate-400 group-focus-within:text-green-400 transition-colors duration-300" />
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="أدخل كلمة المرور"
                    value={formData.password}
                    onChange={(e) => updateFormData("password", e.target.value)}
                    className="pr-10 pl-10 bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 focus:border-green-400 focus:ring-green-400/20 transition-all duration-300"
                    required
                  />
                  <motion.button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute left-3 top-3 text-slate-400 hover:text-green-400 transition-colors duration-300"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </motion.button>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1 }}
                className="space-y-2"
              >
                <Label htmlFor="confirmPassword" className="text-white">
                  تأكيد كلمة المرور
                </Label>
                <div className="relative group">
                  <Lock className="absolute right-3 top-3 h-4 w-4 text-slate-400 group-focus-within:text-green-400 transition-colors duration-300" />
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="أعد إدخال كلمة المرور"
                    value={formData.confirmPassword}
                    onChange={(e) => updateFormData("confirmPassword", e.target.value)}
                    className="pr-10 pl-10 bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 focus:border-green-400 focus:ring-green-400/20 transition-all duration-300"
                    required
                  />
                  <motion.button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute left-3 top-3 text-slate-400 hover:text-green-400 transition-colors duration-300"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </motion.button>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.1 }}
                className="flex items-center space-x-2 space-x-reverse"
              >
                <Checkbox
                  id="terms"
                  checked={formData.agreeToTerms}
                  onCheckedChange={(checked) => updateFormData("agreeToTerms", checked as boolean)}
                  className="border-slate-600 data-[state=checked]:bg-green-400 data-[state=checked]:border-green-400"
                />
                <Label htmlFor="terms" className="text-sm text-slate-300">
                  أوافق على{" "}
                  <Link href="/terms" className="text-green-400 hover:text-green-300 transition-colors duration-300">
                    الشروط والأحكام
                  </Link>
                </Label>
              </motion.div>

              <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 1.2 }}>
                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                  <Button
                    type="submit"
                    disabled={!formData.agreeToTerms || isLoading}
                    className="w-full bg-gradient-to-r from-green-400 to-emerald-500 text-slate-900 hover:from-green-500 hover:to-emerald-600 transition-all duration-300 hover:shadow-[0_0_30px_rgba(34,197,94,0.6)] relative overflow-hidden"
                  >
                    {isLoading && (
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-green-300 to-emerald-400"
                        animate={{
                          x: ["-100%", "100%"],
                        }}
                        transition={{
                          duration: 1.5,
                          repeat: Number.POSITIVE_INFINITY,
                          ease: "linear",
                        }}
                      />
                    )}
                    <span className="relative z-10 flex items-center justify-center gap-2">
                      {isLoading ? (
                        <>
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
                            className="w-4 h-4 border-2 border-slate-900 border-t-transparent rounded-full"
                          />
                          جاري إنشاء الحساب...
                        </>
                      ) : (
                        <>
                          <Sparkles className="w-4 h-4" />
                          إنشاء الحساب
                        </>
                      )}
                    </span>
                  </Button>
                </motion.div>
              </motion.div>
            </form>

            {/* Social Login */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.3 }}
              className="space-y-3"
            >
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-slate-600"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-slate-800 text-slate-400">أو سجل عبر</span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => handleSocialLogin("google")}
                    disabled={isLoading}
                    className="w-full border-slate-600 text-white hover:bg-red-500 hover:border-red-500 transition-all duration-300"
                  >
                    <span className="text-lg ml-2">📧</span>
                    Google
                  </Button>
                </motion.div>
                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => handleSocialLogin("facebook")}
                    disabled={isLoading}
                    className="w-full border-slate-600 text-white hover:bg-blue-600 hover:border-blue-600 transition-all duration-300"
                  >
                    <span className="text-lg ml-2">📘</span>
                    Facebook
                  </Button>
                </motion.div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.4 }}
              className="text-center"
            >
              <span className="text-slate-300">لديك حساب بالفعل؟ </span>
              <Link
                href="/auth/login"
                className="text-green-400 hover:text-green-300 transition-colors duration-300 font-semibold"
              >
                تسجيل الدخول
              </Link>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
