"use client"

import type React from "react"
import { useState, useEffect, createContext, useContext } from "react"
import type { User, Order, Transaction, Notification } from "./types"

interface AuthContextType {
  user: User | null
  login: (email: string, password: string) => Promise<boolean>
  loginWithGoogle: () => Promise<boolean>
  loginWithFacebook: () => Promise<boolean>
  register: (userData: RegisterData) => Promise<boolean>
  logout: () => void
  isLoading: boolean
  orders: Order[]
  transactions: Transaction[]
  notifications: Notification[]
  addOrder: (order: Omit<Order, "id" | "userId" | "createdAt" | "progress" | "status">) => Promise<boolean>
  addTransaction: (transaction: Omit<Transaction, "id" | "userId" | "createdAt">) => Promise<boolean>
  addNotification: (notification: Omit<Notification, "id" | "userId" | "createdAt" | "read">) => void
  markNotificationAsRead: (id: string) => void
  clearNotifications: () => void
}

interface RegisterData {
  name: string
  email: string
  password: string
  phone?: string
}

const AuthContext = createContext<AuthContextType | null>(null)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [orders, setOrders] = useState<Order[]>([])
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [notifications, setNotifications] = useState<Notification[]>([])

  useEffect(() => {
    // Check for existing session
    const savedUser = localStorage.getItem("smmElsoury_user")
    if (savedUser) {
      setUser(JSON.parse(savedUser))
    }

    // Load orders
    const savedOrders = localStorage.getItem("smmElsoury_orders")
    if (savedOrders) {
      setOrders(JSON.parse(savedOrders))
    }

    // Load transactions
    const savedTransactions = localStorage.getItem("smmElsoury_transactions")
    if (savedTransactions) {
      setTransactions(JSON.parse(savedTransactions))
    }

    // Load notifications
    const savedNotifications = localStorage.getItem("smmElsoury_notifications")
    if (savedNotifications) {
      setNotifications(JSON.parse(savedNotifications))
    }

    setIsLoading(false)
  }, [])

  // Save data to localStorage whenever it changes
  useEffect(() => {
    if (orders.length > 0) {
      localStorage.setItem("smmElsoury_orders", JSON.stringify(orders))
    }
  }, [orders])

  useEffect(() => {
    if (transactions.length > 0) {
      localStorage.setItem("smmElsoury_transactions", JSON.stringify(transactions))
    }
  }, [transactions])

  useEffect(() => {
    if (notifications.length > 0) {
      localStorage.setItem("smmElsoury_notifications", JSON.stringify(notifications))
    }
  }, [notifications])

  const login = async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true)
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // Demo users
      const demoUsers = [
        {
          id: "1",
          name: "أحمد محمد",
          email: "<EMAIL>",
          role: "user" as const,
          balance: 150.75,
          isVerified: true,
          createdAt: "2024-01-01",
        },
        {
          id: "2",
          name: "المدير العام",
          email: "<EMAIL>",
          role: "admin" as const,
          balance: 10000,
          isVerified: true,
          createdAt: "2024-01-01",
        },
      ]

      const foundUser = demoUsers.find((u) => u.email === email)
      if (foundUser && password === "123456") {
        setUser(foundUser)
        localStorage.setItem("smmElsoury_user", JSON.stringify(foundUser))

        // Add demo data for the user
        if (foundUser.role === "user" && orders.length === 0) {
          const demoOrders: Order[] = [
            {
              id: "ord_" + Date.now(),
              userId: foundUser.id,
              serviceId: 1,
              serviceName: "متابعين إنستغرام - جودة عالية",
              quantity: 1000,
              price: 50,
              link: "https://instagram.com/example",
              status: "مكتمل",
              progress: 100,
              createdAt: new Date(Date.now() - 86400000 * 3).toISOString(),
            },
            {
              id: "ord_" + (Date.now() + 1),
              userId: foundUser.id,
              serviceId: 3,
              serviceName: "إعجابات فيسبوك - سريع",
              quantity: 500,
              price: 15,
              link: "https://facebook.com/post/123",
              status: "قيد التنفيذ",
              progress: 75,
              createdAt: new Date(Date.now() - 86400000 * 1).toISOString(),
            },
          ]
          setOrders(demoOrders)
          localStorage.setItem("smmElsoury_orders", JSON.stringify(demoOrders))

          const demoTransactions: Transaction[] = [
            {
              id: "txn_" + Date.now(),
              userId: foundUser.id,
              type: "شحن",
              amount: 200,
              description: "شحن رصيد عبر فودافون كاش",
              status: "مكتمل",
              paymentMethod: "فودافون كاش",
              paymentId: "VC" + Math.floor(Math.random() * 1000000),
              createdAt: new Date(Date.now() - 86400000 * 5).toISOString(),
            },
            {
              id: "txn_" + (Date.now() + 1),
              userId: foundUser.id,
              type: "طلب",
              amount: -50,
              description: "طلب متابعين إنستغرام - جودة عالية",
              status: "مكتمل",
              createdAt: new Date(Date.now() - 86400000 * 3).toISOString(),
            },
          ]
          setTransactions(demoTransactions)
          localStorage.setItem("smmElsoury_transactions", JSON.stringify(demoTransactions))

          const demoNotifications: Notification[] = [
            {
              id: "notif_" + Date.now(),
              userId: foundUser.id,
              title: "مرحبًا بك في smmElsoury",
              message: "شكرًا لتسجيلك في منصتنا. استمتع بخدماتنا المميزة!",
              type: "success",
              read: false,
              createdAt: new Date().toISOString(),
            },
            {
              id: "notif_" + (Date.now() + 1),
              userId: foundUser.id,
              title: "تم اكتمال طلبك",
              message: "تم اكتمال طلب متابعين إنستغرام بنجاح!",
              type: "info",
              read: false,
              createdAt: new Date(Date.now() - 86400000 * 3).toISOString(),
            },
          ]
          setNotifications(demoNotifications)
          localStorage.setItem("smmElsoury_notifications", JSON.stringify(demoNotifications))
        }

        setIsLoading(false)
        return true
      }

      setIsLoading(false)
      return false
    } catch (error) {
      setIsLoading(false)
      return false
    }
  }

  const loginWithGoogle = async (): Promise<boolean> => {
    setIsLoading(true)
    try {
      // Simulate Google OAuth
      await new Promise((resolve) => setTimeout(resolve, 2000))
      const googleUser = {
        id: "google_" + Date.now(),
        name: "مستخدم Google",
        email: "<EMAIL>",
        role: "user" as const,
        balance: 0,
        isVerified: true,
        createdAt: new Date().toISOString(),
      }
      setUser(googleUser)
      localStorage.setItem("smmElsoury_user", JSON.stringify(googleUser))
      setIsLoading(false)
      return true
    } catch (error) {
      setIsLoading(false)
      return false
    }
  }

  const loginWithFacebook = async (): Promise<boolean> => {
    setIsLoading(true)
    try {
      // Simulate Facebook OAuth
      await new Promise((resolve) => setTimeout(resolve, 2000))
      const facebookUser = {
        id: "facebook_" + Date.now(),
        name: "مستخدم Facebook",
        email: "<EMAIL>",
        role: "user" as const,
        balance: 0,
        isVerified: true,
        createdAt: new Date().toISOString(),
      }
      setUser(facebookUser)
      localStorage.setItem("smmElsoury_user", JSON.stringify(facebookUser))
      setIsLoading(false)
      return true
    } catch (error) {
      setIsLoading(false)
      return false
    }
  }

  const register = async (userData: RegisterData): Promise<boolean> => {
    setIsLoading(true)
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000))
      const newUser = {
        id: "user_" + Date.now(),
        name: userData.name,
        email: userData.email,
        role: "user" as const,
        balance: 0,
        isVerified: false,
        createdAt: new Date().toISOString(),
      }
      setUser(newUser)
      localStorage.setItem("smmElsoury_user", JSON.stringify(newUser))

      // Add welcome notification
      const welcomeNotification: Notification = {
        id: "notif_" + Date.now(),
        userId: newUser.id,
        title: "مرحبًا بك في smmElsoury",
        message: "شكرًا لتسجيلك في منصتنا. استمتع بخدماتنا المميزة!",
        type: "success",
        read: false,
        createdAt: new Date().toISOString(),
      }
      setNotifications([welcomeNotification])
      localStorage.setItem("smmElsoury_notifications", JSON.stringify([welcomeNotification]))

      setIsLoading(false)
      return true
    } catch (error) {
      setIsLoading(false)
      return false
    }
  }

  const logout = () => {
    setUser(null)
    localStorage.removeItem("smmElsoury_user")
  }

  const addOrder = async (
    orderData: Omit<Order, "id" | "userId" | "createdAt" | "progress" | "status">,
  ): Promise<boolean> => {
    if (!user) return false

    try {
      // Check if user has enough balance
      if (user.balance < orderData.price) {
        return false
      }

      // Create new order
      const newOrder: Order = {
        id: "ord_" + Date.now(),
        userId: user.id,
        serviceId: orderData.serviceId,
        serviceName: orderData.serviceName,
        quantity: orderData.quantity,
        price: orderData.price,
        link: orderData.link,
        status: "معلق",
        progress: 0,
        createdAt: new Date().toISOString(),
      }

      // Update user balance
      const updatedUser = {
        ...user,
        balance: user.balance - orderData.price,
      }
      setUser(updatedUser)
      localStorage.setItem("smmElsoury_user", JSON.stringify(updatedUser))

      // Add order
      const updatedOrders = [...orders, newOrder]
      setOrders(updatedOrders)
      localStorage.setItem("smmElsoury_orders", JSON.stringify(updatedOrders))

      // Add transaction
      const newTransaction: Transaction = {
        id: "txn_" + Date.now(),
        userId: user.id,
        type: "طلب",
        amount: -orderData.price,
        description: `طلب ${orderData.serviceName}`,
        status: "مكتمل",
        createdAt: new Date().toISOString(),
      }
      const updatedTransactions = [...transactions, newTransaction]
      setTransactions(updatedTransactions)
      localStorage.setItem("smmElsoury_transactions", JSON.stringify(updatedTransactions))

      // Add notification
      const newNotification: Notification = {
        id: "notif_" + Date.now(),
        userId: user.id,
        title: "تم إنشاء طلب جديد",
        message: `تم إنشاء طلب ${orderData.serviceName} بنجاح وسيتم تنفيذه قريبًا.`,
        type: "success",
        read: false,
        createdAt: new Date().toISOString(),
      }
      const updatedNotifications = [...notifications, newNotification]
      setNotifications(updatedNotifications)
      localStorage.setItem("smmElsoury_notifications", JSON.stringify(updatedNotifications))

      return true
    } catch (error) {
      return false
    }
  }

  const addTransaction = async (
    transactionData: Omit<Transaction, "id" | "userId" | "createdAt">,
  ): Promise<boolean> => {
    if (!user) return false

    try {
      // Create new transaction
      const newTransaction: Transaction = {
        id: "txn_" + Date.now(),
        userId: user.id,
        ...transactionData,
        createdAt: new Date().toISOString(),
      }

      // If it's a deposit and status is completed, update user balance
      if (transactionData.type === "شحن" && transactionData.status === "مكتمل") {
        const updatedUser = {
          ...user,
          balance: user.balance + transactionData.amount,
        }
        setUser(updatedUser)
        localStorage.setItem("smmElsoury_user", JSON.stringify(updatedUser))
      }

      // Add transaction
      const updatedTransactions = [...transactions, newTransaction]
      setTransactions(updatedTransactions)
      localStorage.setItem("smmElsoury_transactions", JSON.stringify(updatedTransactions))

      // Add notification
      const newNotification: Notification = {
        id: "notif_" + Date.now(),
        userId: user.id,
        title: transactionData.type === "شحن" ? "تم إضافة رصيد" : "تمت عملية مالية",
        message: transactionData.description,
        type: "info",
        read: false,
        createdAt: new Date().toISOString(),
      }
      const updatedNotifications = [...notifications, newNotification]
      setNotifications(updatedNotifications)
      localStorage.setItem("smmElsoury_notifications", JSON.stringify(updatedNotifications))

      return true
    } catch (error) {
      return false
    }
  }

  const addNotification = (notificationData: Omit<Notification, "id" | "userId" | "createdAt" | "read">) => {
    if (!user) return

    const newNotification: Notification = {
      id: "notif_" + Date.now(),
      userId: user.id,
      ...notificationData,
      read: false,
      createdAt: new Date().toISOString(),
    }

    const updatedNotifications = [...notifications, newNotification]
    setNotifications(updatedNotifications)
    localStorage.setItem("smmElsoury_notifications", JSON.stringify(updatedNotifications))
  }

  const markNotificationAsRead = (id: string) => {
    const updatedNotifications = notifications.map((notification) =>
      notification.id === id ? { ...notification, read: true } : notification,
    )
    setNotifications(updatedNotifications)
    localStorage.setItem("smmElsoury_notifications", JSON.stringify(updatedNotifications))
  }

  const clearNotifications = () => {
    const updatedNotifications = notifications.map((notification) => ({ ...notification, read: true }))
    setNotifications(updatedNotifications)
    localStorage.setItem("smmElsoury_notifications", JSON.stringify(updatedNotifications))
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        login,
        loginWithGoogle,
        loginWithFacebook,
        register,
        logout,
        isLoading,
        orders,
        transactions,
        notifications,
        addOrder,
        addTransaction,
        addNotification,
        markNotificationAsRead,
        clearNotifications,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}
