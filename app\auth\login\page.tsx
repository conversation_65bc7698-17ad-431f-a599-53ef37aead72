"use client"

import type React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription } from "@/components/ui/alert"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { Eye, EyeOff, Mail, Lock, Sparkles, Shield, AlertCircle } from "lucide-react"
import { motion } from "framer-motion"
import { useAuth } from "@/lib/auth"
import SocialLinks from "@/components/social-links"

// Animated Background Component
const AnimatedLoginBackground = () => {
  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none">
      <motion.div
        className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-cyan-400/20 to-blue-600/20 rounded-full blur-3xl"
        animate={{
          x: [0, 100, 0],
          y: [0, -100, 0],
          scale: [1, 1.2, 1],
        }}
        transition={{
          duration: 20,
          repeat: Number.POSITIVE_INFINITY,
          ease: "easeInOut",
        }}
      />
      <motion.div
        className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-purple-400/20 to-pink-600/20 rounded-full blur-3xl"
        animate={{
          x: [0, -100, 0],
          y: [0, 100, 0],
          scale: [1, 1.3, 1],
        }}
        transition={{
          duration: 25,
          repeat: Number.POSITIVE_INFINITY,
          ease: "easeInOut",
        }}
      />
    </div>
  )
}

export default function LoginPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [rememberMe, setRememberMe] = useState(false)
  const [error, setError] = useState("")
  const router = useRouter()
  const { login, loginWithGoogle, loginWithFacebook, isLoading } = useAuth()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")

    const success = await login(email, password)
    if (success) {
      router.push("/dashboard")
    } else {
      setError("بيانات الدخول غير صحيحة. جرب: <EMAIL> / <EMAIL> مع كلمة مرور: 123456")
    }
  }

  const handleGoogleLogin = async () => {
    setError("")
    const success = await loginWithGoogle()
    if (success) {
      router.push("/dashboard")
    } else {
      setError("فشل في تسجيل الدخول عبر Google")
    }
  }

  const handleFacebookLogin = async () => {
    setError("")
    const success = await loginWithFacebook()
    if (success) {
      router.push("/dashboard")
    } else {
      setError("فشل في تسجيل الدخول عبر Facebook")
    }
  }

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center p-4 relative overflow-hidden"
      dir="rtl"
    >
      <AnimatedLoginBackground />

      <motion.div
        initial={{ opacity: 0, y: 50, scale: 0.9 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="relative z-10 w-full max-w-md"
      >
        <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm relative overflow-hidden">
          {/* Animated border */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-cyan-400/50 to-blue-500/50 rounded-lg"
            animate={{
              opacity: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 3,
              repeat: Number.POSITIVE_INFINITY,
              ease: "easeInOut",
            }}
          />
          <div className="absolute inset-[1px] bg-slate-800/90 rounded-lg backdrop-blur-sm" />

          <CardHeader className="text-center relative z-10">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
              className="text-3xl font-bold mb-2"
            >
              <motion.span
                className="bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent"
                animate={{
                  textShadow: [
                    "0 0 10px rgba(6,182,212,0.5)",
                    "0 0 20px rgba(6,182,212,0.8)",
                    "0 0 10px rgba(6,182,212,0.5)",
                  ],
                }}
                transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
              >
                smmElsoury
              </motion.span>
            </motion.div>
            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.5 }}>
              <CardTitle className="text-2xl text-white flex items-center justify-center gap-2">
                <Shield className="w-6 h-6 text-cyan-400" />
                تسجيل الدخول
              </CardTitle>
              <CardDescription className="text-slate-300 mt-2">ادخل إلى حسابك للوصول إلى لوحة التحكم</CardDescription>
            </motion.div>

            {/* Social Links */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
              className="flex justify-center mt-4"
            >
              <SocialLinks />
            </motion.div>
          </CardHeader>

          <CardContent className="relative z-10 space-y-6">
            {error && (
              <motion.div initial={{ opacity: 0, y: -10 }} animate={{ opacity: 1, y: 0 }}>
                <Alert className="border-red-500/50 bg-red-500/10">
                  <AlertCircle className="h-4 w-4 text-red-400" />
                  <AlertDescription className="text-red-400">{error}</AlertDescription>
                </Alert>
              </motion.div>
            )}

            <form onSubmit={handleLogin} className="space-y-6">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6 }}
                className="space-y-2"
              >
                <Label htmlFor="email" className="text-white">
                  البريد الإلكتروني
                </Label>
                <div className="relative group">
                  <Mail className="absolute right-3 top-3 h-4 w-4 text-slate-400 group-focus-within:text-cyan-400 transition-colors duration-300" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="أدخل بريدك الإلكتروني"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="pr-10 bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 focus:border-cyan-400 focus:ring-cyan-400/20 transition-all duration-300"
                    required
                  />
                  <motion.div className="absolute inset-0 bg-gradient-to-r from-cyan-400/10 to-blue-500/10 rounded-md opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none" />
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.7 }}
                className="space-y-2"
              >
                <Label htmlFor="password" className="text-white">
                  كلمة المرور
                </Label>
                <div className="relative group">
                  <Lock className="absolute right-3 top-3 h-4 w-4 text-slate-400 group-focus-within:text-cyan-400 transition-colors duration-300" />
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="أدخل كلمة المرور"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="pr-10 pl-10 bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 focus:border-cyan-400 focus:ring-cyan-400/20 transition-all duration-300"
                    required
                  />
                  <motion.button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute left-3 top-3 text-slate-400 hover:text-cyan-400 transition-colors duration-300"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </motion.button>
                  <motion.div className="absolute inset-0 bg-gradient-to-r from-cyan-400/10 to-blue-500/10 rounded-md opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none" />
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
                className="flex items-center justify-between"
              >
                <div className="flex items-center space-x-2 space-x-reverse">
                  <Checkbox
                    id="remember"
                    checked={rememberMe}
                    onCheckedChange={(checked) => setRememberMe(checked as boolean)}
                    className="border-slate-600 data-[state=checked]:bg-cyan-400 data-[state=checked]:border-cyan-400"
                  />
                  <Label htmlFor="remember" className="text-sm text-slate-300">
                    تذكرني
                  </Label>
                </div>
                <Link
                  href="/auth/forgot-password"
                  className="text-sm text-cyan-400 hover:text-cyan-300 transition-colors duration-300"
                >
                  نسيت كلمة المرور؟
                </Link>
              </motion.div>

              <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.9 }}>
                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="w-full bg-gradient-to-r from-cyan-400 to-blue-500 text-slate-900 hover:from-cyan-500 hover:to-blue-600 transition-all duration-300 hover:shadow-[0_0_30px_rgba(6,182,212,0.6)] relative overflow-hidden"
                  >
                    {isLoading && (
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-cyan-300 to-blue-400"
                        animate={{
                          x: ["-100%", "100%"],
                        }}
                        transition={{
                          duration: 1.5,
                          repeat: Number.POSITIVE_INFINITY,
                          ease: "linear",
                        }}
                      />
                    )}
                    <span className="relative z-10 flex items-center justify-center gap-2">
                      {isLoading ? (
                        <>
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
                            className="w-4 h-4 border-2 border-slate-900 border-t-transparent rounded-full"
                          />
                          جاري تسجيل الدخول...
                        </>
                      ) : (
                        <>
                          <Sparkles className="w-4 h-4" />
                          تسجيل الدخول
                        </>
                      )}
                    </span>
                  </Button>
                </motion.div>
              </motion.div>
            </form>

            {/* Social Login */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1 }}
              className="space-y-3"
            >
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-slate-600"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-slate-800 text-slate-400">أو سجل الدخول عبر</span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleGoogleLogin}
                    disabled={isLoading}
                    className="w-full border-slate-600 text-white hover:bg-red-500 hover:border-red-500 transition-all duration-300"
                  >
                    <span className="text-lg ml-2">📧</span>
                    Google
                  </Button>
                </motion.div>
                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleFacebookLogin}
                    disabled={isLoading}
                    className="w-full border-slate-600 text-white hover:bg-blue-600 hover:border-blue-600 transition-all duration-300"
                  >
                    <span className="text-lg ml-2">📘</span>
                    Facebook
                  </Button>
                </motion.div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.1 }}
              className="text-center"
            >
              <span className="text-slate-300">ليس لديك حساب؟ </span>
              <Link
                href="/auth/register"
                className="text-cyan-400 hover:text-cyan-300 transition-colors duration-300 font-semibold"
              >
                إنشاء حساب جديد
              </Link>
            </motion.div>

            {/* Demo Credentials */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.2 }}
              className="p-3 bg-slate-700/30 rounded-lg border border-slate-600"
            >
              <p className="text-xs text-slate-400 text-center mb-2">للتجربة:</p>
              <div className="text-xs text-slate-300 space-y-1">
                <div>مستخدم: <EMAIL></div>
                <div>أدمن: <EMAIL></div>
                <div>كلمة المرور: 123456</div>
              </div>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
