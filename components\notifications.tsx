"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Bell, Check, X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useAuth } from "@/lib/auth"
import { formatDistanceToNow } from "date-fns"
import { arSA } from "date-fns/locale"

export default function Notifications() {
  const { notifications, markNotificationAsRead, clearNotifications } = useAuth()
  const [isOpen, setIsOpen] = useState(false)

  const unreadCount = notifications.filter((n) => !n.read).length

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "success":
        return <div className="w-2 h-2 rounded-full bg-green-500"></div>
      case "error":
        return <div className="w-2 h-2 rounded-full bg-red-500"></div>
      case "warning":
        return <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
      default:
        return <div className="w-2 h-2 rounded-full bg-blue-500"></div>
    }
  }

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true, locale: arSA })
    } catch (error) {
      return "منذ قليل"
    }
  }

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="icon"
        className="relative"
        onClick={() => setIsOpen(!isOpen)}
        aria-label="الإشعارات"
      >
        <Bell className="h-5 w-5 text-slate-300" />
        {unreadCount > 0 && (
          <Badge className="absolute -top-1 -right-1 w-5 h-5 p-0 flex items-center justify-center bg-red-500 text-white">
            {unreadCount}
          </Badge>
        )}
      </Button>

      <AnimatePresence>
        {isOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-40"
              onClick={() => setIsOpen(false)}
            />
            <motion.div
              initial={{ opacity: 0, y: 10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute left-0 mt-2 w-80 max-h-[70vh] overflow-y-auto rounded-lg border border-slate-700 bg-slate-800 shadow-lg z-50"
            >
              <div className="p-3 border-b border-slate-700 flex items-center justify-between">
                <h3 className="font-medium text-white">الإشعارات</h3>
                <div className="flex items-center gap-2">
                  {unreadCount > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 px-2 text-xs text-slate-300 hover:text-white"
                      onClick={(e) => {
                        e.stopPropagation()
                        clearNotifications()
                      }}
                    >
                      <Check className="h-3.5 w-3.5 mr-1" />
                      تعليم الكل كمقروء
                    </Button>
                  )}
                  <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => setIsOpen(false)}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="py-1">
                {notifications.length === 0 ? (
                  <div className="px-4 py-6 text-center text-slate-400">
                    <p>لا توجد إشعارات</p>
                  </div>
                ) : (
                  notifications
                    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
                    .map((notification) => (
                      <motion.div
                        key={notification.id}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className={`px-4 py-3 hover:bg-slate-700/50 cursor-pointer transition-colors ${
                          !notification.read ? "bg-slate-700/30" : ""
                        }`}
                        onClick={() => markNotificationAsRead(notification.id)}
                      >
                        <div className="flex items-start gap-3">
                          <div className="mt-1.5">{getNotificationIcon(notification.type)}</div>
                          <div className="flex-1 min-w-0">
                            <p className="font-medium text-white truncate">{notification.title}</p>
                            <p className="text-sm text-slate-300 mt-0.5">{notification.message}</p>
                            <p className="text-xs text-slate-400 mt-1">{formatDate(notification.createdAt)}</p>
                          </div>
                          {!notification.read && <div className="w-2 h-2 rounded-full bg-cyan-400 mt-2"></div>}
                        </div>
                      </motion.div>
                    ))
                )}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  )
}
